/**
 * 构建相关配置
 */

/**
 * 创建资源文件命名规则
 * @param {Object} assetInfo 资源信息
 * @returns {string} 资源文件路径
 */
function createAssetFileNames(assetInfo) {
  const fileName = assetInfo.names?.[0] || assetInfo.name || 'unknown'
  const info = fileName.split('.')
  let extType = info[info.length - 1]

  // 媒体资源
  if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(fileName)) {
    extType = 'media'
  }
  // 图片资源
  else if (/\.(png|jpe?g|gif|svg|ico|webp)(\?.*)?$/i.test(fileName)) {
    extType = 'images'
  }
  // 字体资源
  else if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(fileName)) {
    extType = 'fonts'
  }

  return `assets/${extType}/[name]-[hash].[ext]`
}

/**
 * 创建手动分包规则
 * @param {string} id 模块 ID
 * @returns {string|undefined} 分包名称
 */
function createManualChunks(id) {
  // Vue 核心生态 - 包含 Vue、Vue Router、Pinia
  if (id.includes('vue') || id.includes('vue-router') || id.includes('pinia')) {
    return 'vue-core'
  }

  // Element Plus - UI 组件库
  if (id.includes('element-plus')) {
    return 'element-plus'
  }

  // 工具库 - 按使用频率分组
  if (id.includes('dayjs') || id.includes('lodash')) {
    return 'date-utils'
  }

  // 网络和加密库
  if (id.includes('axios') || id.includes('crypto-js')) {
    return 'network-crypto'
  }

  // 动画和文档预览 - 大型库
  if (id.includes('gsap')) {
    return 'animation'
  }

  if (id.includes('docx-preview')) {
    return 'document'
  }

  // 其他第三方库
  if (id.includes('node_modules')) {
    return 'vendor'
  }
}

/**
 * 创建构建配置
 * @param {Object} options 配置选项
 * @param {boolean} options.isProduction 是否为生产环境
 * @returns {Object} 构建配置
 */
export function createBuildConfig(options = {}) {
  const { isProduction = false } = options

  return {
    target: 'es2015',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: !isProduction,
    minify: isProduction ? 'esbuild' : false,

    esbuild: isProduction
      ? {
          drop: ['console', 'debugger'],
          legalComments: 'none'
        }
      : undefined,

    rollupOptions: {
      maxParallelFileOps: 5,
      output: {
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: createAssetFileNames,
        manualChunks: createManualChunks
      }
    },

    chunkSizeWarningLimit: 500,
    reportCompressedSize: false,
    cssCodeSplit: true,

    commonjsOptions: {
      include: [/node_modules/],
      transformMixedEsModules: true
    }
  }
}
