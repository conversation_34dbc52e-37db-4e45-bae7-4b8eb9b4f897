/**
 * 路径别名配置
 */
import { fileURLToPath, URL } from 'node:url'

/**
 * 创建路径别名配置
 * @returns {Object} 别名配置对象
 */
export function createAliasConfig() {
  return {
    '@': fileURLToPath(new URL('../../src', import.meta.url)),
    '@components': fileURLToPath(new URL('../../src/components', import.meta.url)),
    '@views': fileURLToPath(new URL('../../src/views', import.meta.url)),
    '@stores': fileURLToPath(new URL('../../src/stores', import.meta.url)),
    '@assets': fileURLToPath(new URL('../../src/assets', import.meta.url)),
    '@utils': fileURLToPath(new URL('../../src/utils', import.meta.url)),
    '@hooks': fileURLToPath(new URL('../../src/hooks', import.meta.url)),
    '@api': fileURLToPath(new URL('../../src/api', import.meta.url)),
    '@request': fileURLToPath(new URL('../../src/request', import.meta.url))
  }
}
