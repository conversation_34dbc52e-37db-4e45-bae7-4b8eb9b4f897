/**
 * Vite 配置统一管理入口
 */
import { createAliasConfig } from './alias.js'
import { createServerConfig, createPreviewConfig } from './server.js'
import { createBuildConfig } from './build.js'
import { createCSSConfig } from './css.js'
import { createOptimizeDepsConfig, createESBuildConfig } from './optimization.js'
import { getBuildEnv } from '../utils/env.js'

/**
 * 创建完整的 Vite 配置
 * @param {Object} viteConfig Vite 配置对象
 * @param {string} viteConfig.command 构建命令
 * @param {string} viteConfig.mode 构建模式
 * @param {Object} env 环境变量
 * @returns {Object} 完整的配置对象
 */
export function createViteConfig({ command, mode }, env) {
  // 获取环境信息
  const { isProduction } = getBuildEnv(command, mode)

  // 配置对象
  const config = {
    // 基础路径
    base: '/intelligent-penetration/',

    // 环境变量定义
    define: {
      'process.env.NODE_ENV': JSON.stringify(mode)
    },

    // 路径别名
    resolve: {
      alias: createAliasConfig()
    },

    // 开发服务器
    server: createServerConfig(env),

    // 构建配置
    build: createBuildConfig({ isProduction }),

    // 依赖优化
    optimizeDeps: createOptimizeDepsConfig(),

    // 预览服务器
    preview: createPreviewConfig(),

    // CSS 配置
    css: createCSSConfig({ isProduction }),

    // ESBuild 配置
    esbuild: createESBuildConfig({ isProduction })
  }

  // 开发环境日志
  if (!isProduction) {
    console.log('⚙️ 开发环境配置已加载')
  }

  // 生产环境日志
  if (isProduction) {
    console.log('🏗️ 生产环境配置已加载')
  }

  return config
}
