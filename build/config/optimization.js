/**
 * 优化相关配置
 */

/**
 * 创建依赖优化配置
 * @returns {Object} 依赖优化配置
 */
export function createOptimizeDepsConfig() {
  return {
    include: [
      'vue',
      'vue-router',
      'pinia',
      'element-plus',
      '@element-plus/icons-vue',
      'axios',
      'dayjs'
    ],
    exclude: ['@iconify/json'],
    // 启用更激进的依赖优化
    force: true,
    esbuildOptions: {
      target: 'es2015',
      treeShaking: true
    }
  }
}

/**
 * 创建 ESBuild 配置
 * @param {Object} options 配置选项
 * @param {boolean} options.isProduction 是否为生产环境
 * @returns {Object} ESBuild 配置
 */
export function createESBuildConfig(options = {}) {
  const { isProduction = false } = options

  return {
    drop: isProduction ? ['console', 'debugger'] : []
  }
}
