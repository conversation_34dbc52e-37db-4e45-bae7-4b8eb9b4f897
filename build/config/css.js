/**
 * CSS 相关配置
 */
import postcsspxtoviewport from 'postcss-px-to-viewport-8-plugin'

/**
 * 创建 CSS 配置
 * @param {Object} options 配置选项
 * @param {boolean} options.isProduction 是否为生产环境
 * @returns {Object} CSS 配置
 */
export function createCSSConfig(options = {}) {
  const { isProduction = false } = options

  return {
    preprocessorOptions: {
      scss: {
        additionalData: `@use "@/assets/style/mixins.scss" as *;`
      }
    },
    postcss: {
      plugins: [
        // 移除 @charset 规则
        {
          postcssPlugin: 'internal:charset-removal',
          AtRule: {
            charset: (atRule) => {
              if (atRule.name === 'charset') {
                atRule.remove()
              }
            }
          }
        },
        // px 转 vw 响应式处理
        postcsspxtoviewport({
          unitToConvert: 'px',
          viewportWidth: 1920,
          unitPrecision: 5,
          propList: ['*'],
          viewportUnit: 'vw',
          fontViewportUnit: 'vw',
          selectorBlackList: [],
          minPixelValue: 1,
          mediaQuery: false,
          replace: true,
          exclude: [],
          include: undefined,
          landscape: false
        })
      ]
    },
    devSourcemap: !isProduction
  }
}
