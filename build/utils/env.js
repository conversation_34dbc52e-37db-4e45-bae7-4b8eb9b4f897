/**
 * 环境相关工具函数
 */

/**
 * 获取构建环境信息
 * @param {string} command Vite 命令
 * @param {string} mode 构建模式
 * @returns {Object} 环境信息
 */
export function getBuildEnv(command, mode) {
  const isProduction = command === 'build'
  const isDevelopment = command === 'serve'
  const isAnalyze = isProduction && process.env.ANALYZE !== 'false'

  return {
    isProduction,
    isDevelopment,
    isAnalyze,
    mode,
    command
  }
}

/**
 * 检查是否为特定模式
 * @param {string} mode 当前模式
 * @param {string} targetMode 目标模式
 * @returns {boolean}
 */
export function isMode(mode, targetMode) {
  return mode === targetMode
}

/**
 * 获取环境变量
 * @param {string} key 环境变量键
 * @param {string} defaultValue 默认值
 * @returns {string}
 */
export function getEnvVar(key, defaultValue = '') {
  return process.env[key] || defaultValue
}
