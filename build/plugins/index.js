/**
 * Vite 插件统一管理入口
 *
 * 插件分类：
 * - Vue 相关：Vue SFC、JSX、DevTools
 * - UI 相关：自动导入、组件库、图标
 * - 优化相关：压缩、分包、样式处理、Bundle 分析
 */

import { createVuePlugins } from './vue.js'
import { createUIPlugins } from './ui.js'
import { createOptimizationPlugins } from './optimization.js'
import { getBuildEnv } from '../utils/env.js'

/**
 * 创建所有 Vite 插件
 * @param {Object} viteConfig Vite 配置对象
 * @param {string} viteConfig.command 构建命令
 * @param {string} viteConfig.mode 构建模式
 * @returns {Array} 插件数组
 */
export default function createVitePlugins({ command, mode }) {
  const { isProduction, isDevelopment } = getBuildEnv(command, mode)

  const plugins = [
    ...createVuePlugins({ isDev: isDevelopment }),
    ...createUIPlugins(),
    ...createOptimizationPlugins({ isProduction })
  ].filter(Boolean)

  // 开发环境日志
  if (isDevelopment) {
    console.log('🔧 开发环境插件已加载')
  }

  // 生产环境日志
  if (isProduction) {
    console.log('🚀 生产环境插件已加载')
    console.log('📊 Bundle 分析已启用')
    console.log('⚡ 资源预加载插件已启用')
  }

  return plugins
}
