/**
 * Bundle 分析插件
 * 用于分析打包体积和依赖关系
 */
import { visualizer } from 'rollup-plugin-visualizer'

/**
 * 创建 Bundle 分析插件
 * @param {Object} options 配置选项
 * @returns {Array} 插件数组
 */
export function createBundleAnalyzer(options = {}) {
  const { enabled = false, open = true, gzipSize = true, brotliSize = true } = options

  if (!enabled) {
    return []
  }

  return [
    visualizer({
      filename: 'dist/stats.html',
      open,
      gzipSize,
      brotliSize,
      template: 'treemap' //移除可能干扰的选项
    })
  ]
}

export default createBundleAnalyzer
