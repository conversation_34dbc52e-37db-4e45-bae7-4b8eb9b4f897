/**
 * @param {Object} options 配置选项
 * @returns {Object} Vite 插件对象
 */
export function createPreloadPlugin(options = {}) {
  const { enabled = true, assets = [] } = options

  if (!enabled) {
    return {
      name: 'vite-plugin-preload',
      apply: () => false
    }
  }

  if (!assets || assets.length === 0) {
    return {
      name: 'vite-plugin-preload',
      apply: () => false
    }
  }
  let assetMap = new Map()

  return {
    name: 'vite-plugin-preload',
    apply: 'build',

    generateBundle(_, bundle) {
      Object.keys(bundle).forEach((fileName) => {
        const chunk = bundle[fileName]
        if (chunk.type === 'asset') {
          assets.forEach((asset) => {
            const originalName = asset.split('/').pop()
            const baseName = originalName.split('.')[0]
            if (fileName.includes(baseName)) {
              assetMap.set(asset, fileName)
              // console.log(`映射: ${asset} -> ${fileName}`)
            }
          })
        }
      })
    },

    transformIndexHtml(html) {
      console.log('transformIndexHtml 被调用')
      const preloadTags = assets
        .map((asset) => {
          const actualFileName = assetMap.get(asset)
          if (!actualFileName) {
            return null
          }

          const assetType = getAssetType(asset)
          const href = `./${actualFileName}`
          const crossorigin = assetType === 'font' ? ' crossorigin' : ''
          const tag = `  <link rel="preload" href="${href}" as="${assetType}"${crossorigin}>`
          return tag
        })
        .filter(Boolean)
        .join('\n')

      const titleEndIndex = html.indexOf('</title>')

      if (titleEndIndex !== -1) {
        const titleCloseEnd = titleEndIndex + '</title>'.length
        const beforeTitle = html.slice(0, titleCloseEnd)
        const afterTitle = html.slice(titleCloseEnd)
        const result = `${beforeTitle}${preloadTags}${afterTitle}`
        return result
      }
      return html
    }
  }
}

function getAssetType(asset) {
  const ext = asset.split('.').pop()?.toLowerCase()
  const typeMap = {
    jpg: 'image',
    jpeg: 'image',
    png: 'image',
    gif: 'image',
    svg: 'image',
    webp: 'image',
    avif: 'image',
    woff: 'font',
    woff2: 'font',
    ttf: 'font',
    otf: 'font',
    eot: 'font',
    css: 'style',
    js: 'script',
    mjs: 'script',
    json: 'fetch',
    xml: 'fetch'
  }

  return typeMap[ext] || 'fetch'
}
