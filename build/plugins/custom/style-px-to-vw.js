/**
 * Vite 插件：Vue 模板中 px 转 vw 响应式处理
 * 专门处理 Vue 单文件组件模板中的内联样式 px 转换
 */

const defaultOptions = {
  // 基础配置(默认 PC)
  unitToConvert: 'px',
  viewportWidth: 1920,
  unitPrecision: 5,
  viewportUnit: 'vw',
  fontViewportUnit: 'vw',
  minPixelValue: 1,

  // 移动端配置
  rootValue: 16,
  designWidth: 375,

  // 高级配置
  maxPixelValue: Infinity,

  // 排除配置
  propBlackList: [], // 排除的属性名
  valueBlackList: [], // 排除的具体值

  // 功能开关
  transformDecimalPixels: true, // 是否转换小数像素
  transformNegativePixels: true, // 是否转换负数像素

  // 调试配置
  debug: false,
  logTransformations: false
}

/**
 * 高精度数值转换函数
 */
function toFixed(number, precision) {
  const multiplier = Math.pow(10, precision + 1)
  const wholeNumber = Math.floor(number * multiplier)
  return (Math.round(wholeNumber / 10) * 10) / multiplier
}

/**
 * 创建像素值替换函数
 */
function createPxReplace(options) {
  const {
    viewportWidth,
    minPixelValue,
    maxPixelValue,
    unitPrecision,
    viewportUnit,
    fontViewportUnit,
    valueBlackList,
    transformDecimalPixels,
    transformNegativePixels,
    debug,
    rootValue,
    designWidth
  } = options

  return function (match, pixelValue, property = '') {
    if (!pixelValue) return match

    const pixels = parseFloat(pixelValue)

    // 数值验证
    if (isNaN(pixels)) return match

    // 负数处理
    if (pixels < 0 && !transformNegativePixels) return match

    // 小数处理
    if (pixels % 1 !== 0 && !transformDecimalPixels) return match

    // 数值范围检查
    const absPixels = Math.abs(pixels)
    if (absPixels < minPixelValue || absPixels > maxPixelValue) return match

    // 黑名单检查
    if (valueBlackList.includes(pixelValue + 'px')) return match

    // 根据目标单位计算转换值
    let convertedValue, result
    const targetUnit = property.includes('font') ? fontViewportUnit : viewportUnit

    if (targetUnit === 'rem') {
      // px 转 rem：px值 / rootValue
      convertedValue = toFixed(pixels / rootValue, unitPrecision)
      result = convertedValue + 'rem'
    } else if (targetUnit === 'vw') {
      // px 转 vw：(px值 / 设计稿宽度) * 100
      const baseWidth = designWidth || viewportWidth
      convertedValue = toFixed((pixels / baseWidth) * 100, unitPrecision)
      result = convertedValue + 'vw'
    } else {
      // 其他单位
      const baseWidth = designWidth || viewportWidth
      convertedValue = toFixed((pixels / baseWidth) * 100, unitPrecision)
      result = convertedValue + targetUnit
    }

    // 调试日志
    if (debug) {
      console.log(`[px-to-${targetUnit}] ${pixelValue}px -> ${result} (property: ${property})`)
    }

    return result
  }
}

export default function stylePxToVwPlugin(customOptions = {}) {
  const options = { ...defaultOptions, ...customOptions }
  const pxReplace = createPxReplace(options)

  let transformCount = 0
  let fileCount = 0

  return {
    name: 'style-px-to-vw',
    enforce: 'pre',

    buildStart() {
      if (options.debug) {
        console.log('[style-px-to-vw] 插件启动，配置：', options)
      }
    },

    async transform(code, id) {
      if (!/.vue$/.test(id)) return null

      fileCount++
      let hasTransformed = false
      let transformedCode = code

      if (options.debug) {
        console.log(`[style-px-to-vw] 正在处理文件: ${id}`)
        console.log(`[style-px-to-vw] 代码开头: ${code.substring(0, 300)}...`)
      }

      const templateMatch = code.match(/<template[^>]*>([\s\S]*?)<\/template>/i)

      if (!templateMatch) {
        if (options.debug) {
          console.log(`[style-px-to-vw] 文件 ${id} 没有找到模板内容`)
          console.log(`[style-px-to-vw] 代码是否包含 <template>: ${code.includes('<template>')}`)
        }
        return null
      }

      const templateContent = templateMatch[1]
      let newTemplateContent = templateContent

      if (options.debug) {
        console.log(`[style-px-to-vw] 文件 ${id} 模板内容长度: ${templateContent.length}`)
        if (templateContent.includes('style=')) {
          console.log(`[style-px-to-vw] 文件 ${id} 包含 style 属性`)
        }
      }

      const styleRegex = /style\s*=\s*["']([\s\S]*?)["']/gi

      newTemplateContent = newTemplateContent.replace(styleRegex, (match, styleContent) => {
        const newStyleContent = styleContent.replace(
          /([a-z-]+)\s*:\s*([+-]?(?:\d*\.)??\d+)px/gim,
          (styleMatch, property, pixelValue) => {
            // 排除黑名单中的属性
            if (options.propBlackList.includes(property)) {
              return styleMatch
            }

            const result = pxReplace(styleMatch, pixelValue, property)
            if (result !== styleMatch) {
              transformCount++
              hasTransformed = true

              if (options.logTransformations) {
                console.log(`[style-px-to-vw] ${id}: ${property}: ${pixelValue}px -> ${result}`)
              }
            }
            return result
          }
        )

        return match.replace(styleContent, newStyleContent)
      })

      if (hasTransformed) {
        transformedCode = code.replace(templateContent, newTemplateContent)

        if (options.debug) {
          console.log(`[style-px-to-vw] 已处理文件: ${id}`)
        }
      }

      return hasTransformed ? { code: transformedCode } : null
    },

    buildEnd() {
      if (options.debug || options.logTransformations) {
        console.log(`[style-px-to-vw] 构建完成统计:`)
        console.log(`  - 处理文件数: ${fileCount}`)
        console.log(`  - 转换次数: ${transformCount}`)
      }
    }
  }
}
