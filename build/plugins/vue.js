import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vueDevTools from 'vite-plugin-vue-devtools'

/**
 * 创建 Vue 相关插件
 * @param {Object} options 配置选项
 * @param {boolean} options.isDev 是否为开发环境
 * @returns {Array} 插件数组
 */
export function createVuePlugins(options = {}) {
  const { isDev = false } = options

  return [vue(), vueJsx(), isDev && vueDevTools()].filter(Boolean)
}
