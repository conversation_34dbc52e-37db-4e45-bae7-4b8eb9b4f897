/**
 * 优化相关插件配置
 */
import viteCompression from 'vite-plugin-compression'
import { manualChunksPlugin } from 'vite-plugin-webpackchunkname'
import stylePxToVw from './custom/style-px-to-vw.js'
import createBundleAnalyzer from './custom/bundle-analyzer.js'
import { createPreloadPlugin } from './custom/preload.js'

/**
 * 创建优化相关插件
 * @param {Object} options 配置选项
 * @param {boolean} options.isProduction 是否为生产环境
 * @returns {Array} 插件数组
 */
export function createOptimizationPlugins(options = {}) {
  const { isProduction = false } = options

  return [
    stylePxToVw({
      viewportWidth: 1920,
      unitPrecision: 5,
      minPixelValue: 1,
      maxPixelValue: 1000,
      propBlackList: ['border-width', 'outline-width', 'box-shadow', 'text-shadow'],
      valueBlackList: ['1px'],
      transformDecimalPixels: true,
      transformNegativePixels: true,
      debug: false,
      logTransformations: false
    }),
    manualChunksPlugin(),
    isProduction &&
      createPreloadPlugin({
        enabled: true,
        assets: [
          '/src/assets/img/robot-title.svg',
          '/src/assets/img/logo.svg',
          '/src/assets/img/background.svg'
        ]
      }),

    ...createBundleAnalyzer({
      enabled: isProduction,
      open: true
    }),

    // Gzip 压缩 (仅生产环境)
    isProduction &&
      viteCompression({
        algorithm: 'gzip',
        ext: '.gz',
        threshold: 1024,
        deleteOriginFile: false,
        verbose: false
      })
    // Brotli 压缩 (仅生产环境)
    // isProduction &&
    //   viteCompression({
    //     algorithm: 'brotliCompress',
    //     ext: '.br',
    //     threshold: 1024,
    //     deleteOriginFile: false
    //   })
  ].filter(Boolean)
}
