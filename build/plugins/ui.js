/**
 * UI 组件相关插件配置
 */
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import Icons from 'unplugin-icons/vite'
import IconsResolver from 'unplugin-icons/resolver'

/**
 * 创建 UI 相关插件
 * @returns {Array} 插件数组
 */
export function createUIPlugins() {
  return [
    // API 自动导入
    AutoImport({
      imports: ['vue', 'vue-router', 'pinia'],
      resolvers: [
        ElementPlusResolver(),
        IconsResolver({
          prefix: 'Icon'
        })
      ],
      dts: false,
      eslintrc: {
        enabled: true,
        filepath: './.eslintrc-auto-import.json',
        globalsPropValue: true
      }
    }),

    // 组件自动导入
    Components({
      resolvers: [
        ElementPlusResolver({
          importStyle: 'sass'
        }),
        IconsResolver({
          enabledCollections: [
            'ep',
            'tabler',
            'svg-spinners',
            'material-icon-theme',
            'i-svg-spinners',
            'majesticons'
          ]
        })
      ],
      dts: true,
      include: [/\.vue$/, /\.vue\?/]
    }),

    // 图标自动安装
    Icons({
      autoInstall: true
    })
  ]
}
