/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AnimatedList: typeof import('./src/components/AnimatedList.vue')['default']
    DocumentPreviewDrawer: typeof import('./src/components/TaskExecution/DocumentPreviewDrawer.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ExecutionTimer: typeof import('./src/components/TaskExecution/ExecutionTimer.vue')['default']
    IEpArrowDown: typeof import('~icons/ep/arrow-down')['default']
    IEpDelete: typeof import('~icons/ep/delete')['default']
    IEpDocument: typeof import('~icons/ep/document')['default']
    IEpPlus: typeof import('~icons/ep/plus')['default']
    IEpRefresh: typeof import('~icons/ep/refresh')['default']
    IEpSearch: typeof import('~icons/ep/search')['default']
    IEpSwitchButton: typeof import('~icons/ep/switch-button')['default']
    IEpUser: typeof import('~icons/ep/user')['default']
    IEpWarningFilled: typeof import('~icons/ep/warning-filled')['default']
    'IMajesticons:restrictedLine': typeof import('~icons/majesticons/restricted-line')['default']
    ISvgSpinners12DotsScaleRotate: typeof import('~icons/svg-spinners/12-dots-scale-rotate')['default']
    ISvgSpinners3DotsScale: typeof import('~icons/svg-spinners/3-dots-scale')['default']
    ISvgSpinners3DotsScaleMiddle: typeof import('~icons/svg-spinners/3-dots-scale-middle')['default']
    ISvgSpinnersBlocksShuffle3: typeof import('~icons/svg-spinners/blocks-shuffle3')['default']
    'ITabler:location': typeof import('~icons/tabler/location')['default']
    'ITabler:square': typeof import('~icons/tabler/square')['default']
    LoadingIndicator: typeof import('./src/components/common/LoadingIndicator.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SystemMessage: typeof import('./src/components/TaskExecution/SystemMessage.vue')['default']
    TaskExecution: typeof import('./src/components/TaskExecution/index.vue')['default']
    TaskForm: typeof import('./src/components/TaskForm.vue')['default']
    TaskList: typeof import('./src/components/TaskList.vue')['default']
    TaskSearch: typeof import('./src/components/taskSearch.vue')['default']
    UserDropdown: typeof import('./src/components/UserDropdown.vue')['default']
    UserMessage: typeof import('./src/components/TaskExecution/UserMessage.vue')['default']
  }
}
