{"extends": ["stylelint-config-standard-scss", "stylelint-config-recess-order", "stylelint-config-prettier-scss"], "plugins": ["stylelint-scss"], "rules": {"no-descending-specificity": null, "selector-class-pattern": null, "custom-property-pattern": null, "keyframes-name-pattern": null, "scss/dollar-variable-pattern": null, "scss/at-mixin-pattern": null, "scss/at-function-pattern": null, "scss/at-rule-no-unknown": true, "scss/load-partial-extension": "never", "selector-pseudo-class-no-unknown": [true, {"ignorePseudoClasses": ["deep", "global"]}]}, "ignoreFiles": ["node_modules/**/*", "dist/**/*", "build/**/*", "**/*.js", "**/*.jsx", "**/*.ts", "**/*.tsx"], "overrides": [{"files": ["*.vue", "**/*.vue"], "customSyntax": "postcss-html"}, {"files": ["*.scss", "**/*.scss"], "customSyntax": "postcss-scss"}]}