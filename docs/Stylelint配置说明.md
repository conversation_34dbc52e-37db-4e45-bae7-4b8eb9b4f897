# Stylelint 配置说明

## 📋 概述

Stylelint 是一个强大的现代 CSS 代码检查工具，帮助团队维护一致的样式代码规范。本项目已完整配置 Stylelint，支持 CSS、SCSS 和 Vue 单文件组件中的样式检查。

## 🚀 主要优势

### 1. **代码质量保障**

- **语法错误检测**: 自动发现 CSS/SCSS 语法错误
- **最佳实践强制**: 强制执行 CSS 编写最佳实践
- **兼容性检查**: 确保样式代码的浏览器兼容性

### 2. **团队协作优化**

- **统一代码风格**: 确保团队成员编写风格一致的样式代码
- **自动格式化**: 保存时自动修复可修复的样式问题
- **规范化命名**: 统一类名、变量名等命名规范

### 3. **开发效率提升**

- **实时反馈**: 编辑器中实时显示样式问题
- **自动修复**: 大部分问题可以自动修复
- **Git 集成**: 提交前自动检查，防止问题代码进入仓库

### 4. **维护性增强**

- **可读性提升**: 强制执行缩进、空格等格式规范
- **结构优化**: 属性排序规则提升代码可读性
- **注释规范**: 统一注释格式和风格

## 🛠️ 配置详情

### 核心配置文件

```json
// .stylelintrc.json
{
  "extends": [
    "stylelint-config-standard-scss", // SCSS 标准配置
    "stylelint-config-recess-order", // CSS 属性排序
    "stylelint-config-prettier-scss" // 与 Prettier 兼容
  ],
  "plugins": ["stylelint-scss"],
  "rules": {
    // 自定义规则配置
    "selector-pseudo-class-no-unknown": [
      true,
      { "ignorePseudoClasses": ["deep", "global"] } // 支持 Vue 深度选择器
    ]
  }
}
```

### 支持的文件类型

- **CSS 文件** (`.css`)
- **SCSS 文件** (`.scss`)
- **Vue 单文件组件** (`.vue` 中的 `<style>` 块)

### 属性排序规则

采用 Recess 排序规则，按以下顺序排列 CSS 属性：

1. **定位属性**: `position`, `top`, `right`, `bottom`, `left`, `z-index`
2. **盒模型**: `display`, `width`, `height`, `margin`, `padding`, `border`
3. **字体排版**: `font`, `line-height`, `text-align`, `color`
4. **背景**: `background`, `background-color`, `background-image`
5. **其他**: `opacity`, `transform`, `transition`

## 📝 使用指南

### 命令行使用

```bash
# 检查所有样式文件
pnpm lint:style

# 检查并自动修复
pnpm lint:style --fix

# 完整代码检查 (ESLint + Stylelint)
pnpm lint:all
```

### VSCode 集成

项目已配置 VSCode 设置，安装推荐扩展后即可享受：

- **实时错误提示**: 编辑时实时显示样式问题
- **保存时自动修复**: 保存文件时自动修复可修复的问题
- **语法高亮**: 更好的 SCSS 语法高亮支持

### Git Hooks 集成

通过 `lint-staged` 配置，在 Git 提交前自动检查样式文件：

```json
{
  "*.{css,scss,vue}": ["stylelint --fix", "prettier --write"]
}
```

## 🔧 自定义配置

### 添加自定义规则

在 `.stylelintrc.json` 的 `rules` 部分添加：

```json
{
  "rules": {
    "color-hex-length": "short", // 强制使用短十六进制颜色
    "declaration-block-no-duplicate-properties": true, // 禁止重复属性
    "max-nesting-depth": 3 // 限制嵌套深度
  }
}
```

### 忽略特定文件

在 `.stylelintignore` 文件中添加：

```
# 忽略第三方库
node_modules/
public/lib/

# 忽略构建输出
dist/
build/
```

## 📊 常见问题解决

### 1. Vue 深度选择器报错

**问题**: `:deep()` 选择器被标记为未知伪类

**解决**: 已在配置中添加忽略规则：

```json
{
  "selector-pseudo-class-no-unknown": [true, { "ignorePseudoClasses": ["deep", "global"] }]
}
```

### 2. SCSS 变量命名规则

**问题**: SCSS 变量命名不符合规范

**解决**: 已禁用严格的变量命名规则：

```json
{
  "scss/dollar-variable-pattern": null
}
```

### 3. 属性排序问题

**问题**: CSS 属性顺序不符合规范

**解决**: 使用 `--fix` 参数自动修复：

```bash
pnpm lint:style --fix
```

## 🎯 最佳实践

### 1. 样式编写规范

```scss
// ✅ 推荐写法
.component-name {
  // 定位
  position: relative;
  top: 0;

  // 盒模型
  display: flex;
  width: 100%;
  height: auto;
  margin: 0;
  padding: 16px;

  // 字体
  font-size: 14px;
  color: #333;

  // 背景
  background-color: #fff;

  // 嵌套选择器
  .header {
    margin-bottom: 12px;

    .title {
      font-weight: 600;
    }
  }
}
```

### 2. 颜色使用规范

```scss
// ✅ 使用变量定义颜色
$primary-color: #409eff;
$success-color: #67c23a;

.button {
  background-color: $primary-color;
  color: #fff; // 简写十六进制
}
```

### 3. 响应式设计

```scss
// ✅ 移动端优先
.component {
  padding: 10px;

  @media (min-width: 768px) {
    padding: 20px;
  }
}
```

## 🔄 持续改进

Stylelint 配置会根据项目需求和团队反馈持续优化：

- **规则调整**: 根据实际使用情况调整规则严格程度
- **性能优化**: 优化检查性能，减少构建时间
- **工具集成**: 集成更多开发工具和 CI/CD 流程

---
