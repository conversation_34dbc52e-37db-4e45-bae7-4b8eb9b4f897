# Git 提交规范指南

## 📋 概述

本项目采用约定式提交规范 (Conventional Commits)，通过 Git Hooks 自动化校验提交信息格式和代码质量。

## 🛠️ 工具链

- **husky**: Git Hooks 管理工具
- **@commitlint/cli**: 提交信息校验工具
- **@commitlint/config-conventional**: 约定式提交规范配置
- **lint-staged**: 暂存文件检查工具
- **cz-customizable**: 自定义交互式提交工具
- **ESLint + Prettier**: 代码质量检查和格式化

## 📝 提交格式

### 基本格式

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### 示例

```bash
feat: 新增用户登录功能
fix: 修复登录验证bug
docs: 更新API文档
style: 修复代码格式问题
refactor: 重构用户模块
test: 添加单元测试
chore: 更新构建配置
```

**注意**: 本项目已禁用 scope（范围），提交时无需指定范围。

## 🏷️ Type 类型说明

| Type       | 描述         | 示例                              |
| ---------- | ------------ | --------------------------------- |
| `feat`     | 新增功能     | `feat: 添加用户注册功能`          |
| `fix`      | 修复bug      | `fix: 修复登录页面验证码显示问题` |
| `docs`     | 文档变更     | `docs: 更新API接口文档`           |
| `style`    | 代码格式修改 | `style: 修复代码缩进问题`         |
| `refactor` | 代码重构     | `refactor: 重构用户权限模块`      |
| `perf`     | 性能优化     | `perf: 优化列表查询性能`          |
| `test`     | 测试相关     | `test: 添加用户模块单元测试`      |
| `build`    | 构建相关     | `build: 升级webpack到5.0`         |
| `ci`       | CI/CD相关    | `ci: 添加自动部署脚本`            |
| `chore`    | 其他修改     | `chore: 更新依赖包版本`           |
| `revert`   | 回滚提交     | `revert: 回滚用户模块重构`        |

## 🎯 配置特色

### 中文交互界面

- 所有提示信息均为中文
- 简化的提交流程
- 直观的操作体验

### 简化配置

- **无 scope 要求**: 跳过范围选择，简化提交流程
- **智能跳过**: 自动跳过 body、breaking、footer 等可选项
- **快速确认**: 简化的确认流程

## 🚀 使用方法

### 方法一：使用交互式提交 (推荐)

```bash
# 添加文件到暂存区
git add .

# 使用中文交互式提交
pnpm commit
```

**交互流程**：

1. 选择提交类型（中文界面）
2. 填写变更描述
3. 确认提交信息
4. 自动运行代码检查和格式化

### 方法二：手动编写提交信息

```bash
# 添加文件到暂存区
git add .

# 手动编写符合规范的提交信息
git commit -m "feat: 新增用户登录功能"
```

## 🔍 自动化检查

### Pre-commit 检查

提交前会自动执行以下检查：

- ESLint 代码检查
- Prettier 代码格式化
- 只检查暂存区文件

### Commit-msg 检查

提交时会自动校验：

- 提交信息格式是否符合规范
- Type 是否在允许范围内
- Subject 长度是否超限

## � 配置文件说明

### 核心配置文件

- **`.cz-config.cjs`**: Commitizen 自定义配置，定义中文交互界面
- **`commitlint.config.js`**: 提交信息校验规则
- **`.husky/`**: Git Hooks 脚本目录
- **`package.json`**: 项目配置和脚本定义

## ⚙️ 配置文件说明

### commitlint.config.js

提交信息校验规则配置

### .husky/

Git Hooks 脚本目录

- `pre-commit`: 提交前代码检查
- `commit-msg`: 提交信息校验

### package.json

```json
{
  "scripts": {
    "commit": "git-cz",
    "commitlint": "commitlint --edit",
    "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s"
  },
  "config": {
    "commitizen": {
      "path": "cz-conventional-changelog"
    }
  },
  "lint-staged": {
    "*.{js,jsx,ts,tsx,vue}": ["eslint --fix", "prettier --write"],
    "*.{css,scss,less,html,json,md}": ["prettier --write"]
  }
}
```

## 🚫 常见错误

### 1. 提交信息格式错误

```bash
❌ 错误示例:
git commit -m "修复登录bug"
git commit -m "add new feature"

✅ 正确示例:
git commit -m "fix: 修复登录验证bug"
git commit -m "feat: 新增用户注册功能"
```

### 2. Subject 过长

```bash
❌ 错误示例:
git commit -m "feat: 新增用户登录功能包括用户名密码验证、记住密码、忘记密码等完整功能"

✅ 正确示例:
git commit -m "feat: 新增用户登录功能"
```

### 3. 代码格式问题

```bash
# 如果pre-commit检查失败，先修复代码格式
pnpm lint
pnpm format

# 然后重新提交
git add .
git commit -m "fix: 修复代码格式问题"
```

## 🔧 故障排除

### 1. Husky 不工作

```bash
# 重新安装 husky
pnpm exec husky install

# 检查 .git/hooks 目录权限
ls -la .git/hooks/
```

### 2. Commitlint 校验失败

```bash
# 检查配置文件
cat commitlint.config.js

# 手动测试提交信息
echo "feat: test message" | pnpm exec commitlint
```

### 3. Lint-staged 检查失败

```bash
# 手动运行检查
pnpm exec lint-staged

# 修复代码问题
pnpm lint
pnpm format
```

## 📚 参考资料

- [Conventional Commits](https://www.conventionalcommits.org/)
- [Angular Commit Message Guidelines](https://github.com/angular/angular/blob/master/CONTRIBUTING.md#commit)
- [Commitlint](https://commitlint.js.org/)
- [Husky](https://typicode.github.io/husky/)
- [Lint-staged](https://github.com/okonet/lint-staged)

---

**最后更新**: 2025-07-19  
**维护者**: 暖心
