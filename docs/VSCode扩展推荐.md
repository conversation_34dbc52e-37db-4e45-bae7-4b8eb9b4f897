# VSCode 扩展推荐

## 📋 概述

本项目为 Vue 3 + Vite + Element Plus 技术栈，以下是根据项目需求精心挑选的 VSCode 扩展推荐列表。

## 🔧 核心开发扩展

### 1. Vue.volar

- **扩展ID**: `Vue.volar`
- **作用**: Vue 3 官方扩展（原名 Volar）
- **功能**:
  - Vue 3 语法高亮和智能提示
  - Composition API 支持
  - TypeScript 集成
  - 模板类型检查
  - 组件自动补全

### 2. esbenp.prettier-vscode

- **扩展ID**: `esbenp.prettier-vscode`
- **作用**: 代码格式化工具
- **功能**:
  - 自动格式化 JavaScript、Vue、CSS、SCSS 等文件
  - 保存时自动格式化
  - 与项目 Prettier 配置集成

### 3. dbaeumer.vscode-eslint

- **扩展ID**: `dbaeumer.vscode-eslint`
- **作用**: JavaScript/Vue 代码检查
- **功能**:
  - 实时显示 ESLint 错误和警告
  - 自动修复可修复的问题
  - 与项目 ESLint 配置集成

### 4. stylelint.vscode-stylelint

- **扩展ID**: `stylelint.vscode-stylelint`
- **作用**: CSS/SCSS 样式代码检查
- **功能**:
  - 实时显示样式代码问题
  - 自动修复样式格式问题
  - 支持 SCSS 和 Vue 单文件组件

## 🚀 开发效率扩展

### 5. formulahendry.auto-rename-tag

- **扩展ID**: `formulahendry.auto-rename-tag`
- **作用**: 自动重命名配对的 HTML/XML 标签
- **功能**:
  - 修改开始标签时自动更新结束标签
  - 支持 Vue 模板语法
  - 提高 HTML 编辑效率

### 6. christian-kohler.path-intellisense

- **扩展ID**: `christian-kohler.path-intellisense`
- **作用**: 路径智能提示
- **功能**:
  - 文件路径自动补全
  - 支持相对路径和绝对路径
  - 支持项目别名（@/ 等）

### 7. usernamehw.errorlens

- **扩展ID**: `usernamehw.errorlens`
- **作用**: 行内错误显示
- **功能**:
  - 在代码行末显示错误信息
  - 高亮显示问题代码
  - 提供快速修复建议

### 8. antfu.iconify

- **扩展ID**: `antfu.iconify`
- **作用**: Iconify 图标预览
- **功能**:
  - 预览 Iconify 图标
  - 图标搜索和插入
  - 支持项目中使用的图标库

### 9. antfu.goto-alias

- **扩展ID**: `antfu.goto-alias`
- **作用**: 路径别名跳转支持
- **功能**:
  - 支持 @ 别名跳转
  - 快速导航到文件
  - 与 Vite 别名配置集成

## 📁 文件支持扩展

### 10. ms-vscode.vscode-json

- **扩展ID**: `ms-vscode.vscode-json`
- **作用**: JSON 文件支持
- **功能**:
  - JSON 语法高亮
  - 格式验证
  - 智能提示

### 11. redhat.vscode-yaml

- **扩展ID**: `redhat.vscode-yaml`
- **作用**: YAML 文件支持
- **功能**:
  - YAML 语法高亮
  - 格式验证
  - 智能提示

## 🔧 安装方式

### 自动安装（推荐）

1. 打开项目根目录
2. VSCode 会自动提示安装推荐扩展
3. 点击"安装所有推荐扩展"

### 手动安装

1. 打开 VSCode 扩展面板（Ctrl+Shift+X）
2. 搜索扩展 ID
3. 点击安装

### 批量安装命令

```bash
# 使用 VSCode 命令行工具批量安装
code --install-extension Vue.volar
code --install-extension esbenp.prettier-vscode
code --install-extension dbaeumer.vscode-eslint
code --install-extension stylelint.vscode-stylelint
code --install-extension formulahendry.auto-rename-tag
code --install-extension christian-kohler.path-intellisense
code --install-extension usernamehw.errorlens
code --install-extension antfu.iconify
code --install-extension antfu.goto-alias
code --install-extension ms-vscode.vscode-json
code --install-extension redhat.vscode-yaml
```

## ⚙️ 配置说明

项目已在 `.vscode/settings.json` 中配置了相关扩展的设置：

- **保存时自动格式化**: 使用 Prettier
- **保存时自动修复**: ESLint 和 Stylelint
- **禁用内置验证**: 避免与扩展冲突
- **文件关联**: 正确识别 Vue 文件类型

## 🎯 使用技巧

### 1. 快捷键

- `Ctrl+Shift+P`: 打开命令面板
- `Alt+Shift+F`: 格式化文档
- `Ctrl+.`: 快速修复
- `F2`: 重命名符号

### 2. 工作区设置

- 扩展设置会自动应用到项目
- 团队成员使用相同的开发环境
- 确保代码风格一致性

### 3. 性能优化

- 只安装必要的扩展
- 定期更新扩展版本
- 关闭不需要的扩展功能

## 🔄 扩展更新

建议定期更新扩展以获得最新功能和修复：

1. 打开扩展面板
2. 点击"更新"按钮
3. 重启 VSCode 应用更改

---
