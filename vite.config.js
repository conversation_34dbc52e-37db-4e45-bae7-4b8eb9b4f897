import { defineConfig, loadEnv } from 'vite'
import createVitePlugins from './build/plugins/index.js'
import { createViteConfig } from './build/config/index.js'

export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd())

  const baseConfig = createViteConfig({ command, mode }, env)

  baseConfig.plugins = createVitePlugins({ command, mode })

  return baseConfig
})
