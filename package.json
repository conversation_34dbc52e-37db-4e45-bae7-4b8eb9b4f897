{"name": "intelligent-penetration", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:test": "vite build --mode test", "build:prod": "vite build --mode production", "preview": "vite preview", "lint": "eslint . --fix", "lint:style": "stylelint \"src/**/*.{css,scss,vue}\" --fix", "lint:all": "npm run lint && npm run lint:style", "format": "prettier --write src/", "clean": "<PERSON><PERSON><PERSON> dist", "build:clean": "npm run clean && npm run build", "prepare": "husky", "commit": "git-cz", "commitlint": "commitlint --edit"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.10.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "docx-preview": "^0.3.5", "element-plus": "^2.10.4", "gsap": "^3.13.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.31.0", "@iconify/json": "^2.2.359", "@vitejs/plugin-vue": "^6.0.0", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vue/eslint-config-prettier": "^10.2.0", "commitizen": "^4.3.1", "cross-env": "^10.0.0", "cz-customizable": "^7.4.0", "eslint": "^9.31.0", "eslint-plugin-vue": "~10.3.0", "globals": "^16.3.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "postcss-html": "^1.8.0", "postcss-px-to-viewport-8-plugin": "^1.2.5", "postcss-scss": "^4.0.9", "prettier": "3.6.2", "rimraf": "^6.0.1", "rollup-plugin-brotli": "^3.1.0", "rollup-plugin-visualizer": "^6.0.3", "stylelint": "^16.22.0", "stylelint-config-prettier-scss": "^1.0.0", "stylelint-config-recess-order": "^7.1.0", "stylelint-config-standard-scss": "^15.0.1", "stylelint-scss": "^6.12.1", "unplugin-auto-import": "^19.3.0", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-devtools": "^7.7.7", "vite-plugin-webpackchunkname": "^1.0.3"}, "config": {"commitizen": {"path": "cz-customizable"}, "cz-customizable": {"config": ".cz-config.cjs"}}, "lint-staged": {"*.{js,jsx,ts,tsx,vue}": ["eslint --fix", "prettier --write"], "*.{css,scss,vue}": ["stylelint --fix", "prettier --write"], "*.{less,html,json,md}": ["prettier --write"]}}