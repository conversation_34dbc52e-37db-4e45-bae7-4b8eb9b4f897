/**
 * Commitizen 自定义配置文件
 * 用于配置交互式提交工具的中文界面
 */

module.exports = {
  // 提交类型
  types: [
    { value: 'feat', name: 'feat:     ✨ 新增功能' },
    { value: 'fix', name: 'fix:      🐛 修复缺陷' },
    { value: 'docs', name: 'docs:     📝 文档变更' },
    { value: 'style', name: 'style:    💄 代码格式' },
    { value: 'refactor', name: 'refactor: ♻️  代码重构' },
    { value: 'perf', name: 'perf:     ⚡️ 性能优化' },
    { value: 'test', name: 'test:     ✅ 添加疏漏测试或已有测试改动' },
    { value: 'build', name: 'build:    📦️ 构建流程、外部依赖变更' },
    { value: 'ci', name: 'ci:       🎡 修改 CI 配置、脚本' },
    { value: 'revert', name: 'revert:   ⏪️ 回滚 commit' },
    { value: 'chore', name: 'chore:    🔨 对构建过程或辅助工具和库的更改' },
  ],

  // 不使用 scope
  scopes: [],

  // 是否允许自定义 scope
  allowCustomScopes: false,

  // 是否允许空的 scope
  allowEmptyScopes: true,

  // 是否跳过 scope 选择
  skipScope: true,

  // 提示信息
  messages: {
    type: '选择你要提交的类型:',
    subject: '填写简短精炼的变更描述:\n',
    body: '填写更加详细的变更描述 (可选)。使用 "|" 换行:\n',
    breaking: '列举非兼容性重大的变更 (可选):\n',
    footer: '列举出所有变更的 ISSUES CLOSED (可选)。 例如: #31, #34:\n',
    confirmCommit: '确认使用以上信息提交？(y/n/e/h)',
  },

  // 是否允许破坏性变更
  allowBreakingChanges: ['feat', 'fix'],

  // 跳过的问题
  skipQuestions: ['scope', 'body', 'breaking', 'footer'],

  // subject 最大长度
  subjectLimit: 50,

  // 是否在 subject 末尾添加句号
  subjectSeparator: ': ',

  // 是否将 type 转换为小写
  typePrefix: '',
  typeSuffix: '',

  // 是否在 body 前添加空行
  bodyLineLength: 100,

  // 是否在 footer 前添加空行
  footerPrefix: '',
}
