import { defineStore } from 'pinia'
import { useTaskStore } from './taskStore'

export const useAuthStore = defineStore(
  'auth',
  () => {
    const user = ref(null)
    const token = ref('')
    const isLoggingOut = ref(false)
    const isLoggedIn = computed(() => !!token.value && !isLoggingOut.value)

    const login = (userData) => {
      user.value = userData
      token.value = userData.token
      console.log('用户登录成功:', userData.username)
    }

    const logout = () => {
      isLoggingOut.value = true

      try {
        const taskStore = useTaskStore()
        taskStore.resetStore()

        user.value = null
        token.value = ''

        localStorage.removeItem('intelligent-penetration-auth-store')
        localStorage.removeItem('intelligent-penetration-task-store')

        sessionStorage.clear()

        const keysToRemove = []
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          if (key && key.includes('intelligent-penetration')) {
            keysToRemove.push(key)
          }
        }
        keysToRemove.forEach((key) => localStorage.removeItem(key))

        console.log('用户已登出，所有缓存已清除')
      } catch (error) {
        console.error('登出过程中发生错误:', error)
      } finally {
        // 重置登出状态
        nextTick(() => {
          isLoggingOut.value = false
        })
      }
    }

    // 检查登录状态
    const checkAuth = () => {
      return isLoggedIn.value
    }

    // 获取用户信息
    const getUserInfo = () => {
      return user.value
    }

    return {
      // 状态
      user,
      token,
      isLoggedIn,
      isLoggingOut,

      // 方法
      login,
      logout,
      checkAuth,
      getUserInfo
    }
  },
  {
    // 数据持久化配置
    persist: {
      key: 'intelligent-penetration-auth-store',
      storage: localStorage,
      pick: ['user', 'token']
    }
  }
)
