export default class PromiseState {
  constructor() {
    this.p = false // 进行进行中标准，pending, processing
    this.o = false // 执行成功 ok 标志, promise resolved
    this.e = false // 执行结果报错出现异常 error 标志, promise rejected，可能是 Error 类对象
    this.c = '' // code 错误码，成功为 "0000"，失败为 "9999" 或其他错误码
    this.h = {} // 响应头信息
    this.m = '' // message 调用结果说明，通常出错时给出
    this.d = {} // data 调用结果响应，初始化为空对象
    this.s = 0 // sequence 第几次调用
    this.b = undefined // begin time
    this.f = undefined // finish time
    // 以下是可选成员，用于关联请求相关信息
    this.t = '' // type
    this.u = '' // url, path
    this.up = '' // url prefix for ipu background worker
    this.r = {} // request
  }
}
