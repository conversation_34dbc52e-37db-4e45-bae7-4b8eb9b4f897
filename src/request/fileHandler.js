/**
 * 将对象转换为 FormData
 * @param {Object} params - 需要转换的对象
 * @returns {FormData} 转换后的 FormData 对象
 */
export function convertToFormData(params) {
  const formData = new FormData()
  function appendFormData(data, parentKey = '') {
    if (data === undefined) return // 过滤掉 undefined 的值
    if (data instanceof Blob) {
      formData.append(parentKey, data, `${parentKey}.xlsx`)
    } else if (data && typeof data === 'object' && !Array.isArray(data)) {
      // 处理对象
      Object.keys(data).forEach((key) => {
        const fullKey = parentKey ? `${parentKey}.${key}` : key
        appendFormData(data[key], fullKey)
      })
    } else if (Array.isArray(data)) {
      // 处理数组
      data.forEach((item, index) => {
        const fullKey = `${parentKey}[${index}]`
        // 处理 Blob 对象
        if (item instanceof Blob) {
          formData.append(fullKey, item)
        } else {
          appendFormData(item, fullKey)
        }
      })
    } else {
      // 处理基本数据类型
      formData.append(parentKey, data)
    }
  }
  appendFormData(params)
  return formData
}

/**
 * 检测文件类型并返回适当的内容类型
 * @param {string} fileName - 文件名
 * @param {string} defaultContentType - 默认内容类型
 * @returns {string} 检测到的内容类型
 */
export const detectContentType = (fileName, defaultContentType = 'application/octet-stream') => {
  if (!fileName) return defaultContentType

  const contentTypeMap = {
    zip: 'application/zip',
    xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    xls: 'application/vnd.ms-excel',
    pdf: 'application/pdf',
    docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    doc: 'application/msword'
  }

  const extension = Object.keys(contentTypeMap).find(
    (ext) => fileName.endsWith(`.${ext}`) || fileName.includes(`.${ext}`)
  )

  return extension ? contentTypeMap[extension] : defaultContentType
}

/**
 * 从内容处置头部提取文件名
 * @param {string} contentDisposition - 内容处置头部
 * @param {string} customeFileName - 自定义文件名
 * @returns {string} 提取的文件名或空字符串
 */
export const extractFileName = (contentDisposition, customeFileName) => {
  if (!contentDisposition) return customeFileName

  const fileNameMatch = contentDisposition.match(/filename="?([^"]*)"?$/)
  if (!customeFileName) return fileNameMatch ? fileNameMatch[1] : ''
  return fileNameMatch ? '_' + fileNameMatch[1] : customeFileName
}

/**
 * 下载文件
 * @param {Blob} blob - 文件数据
 * @param {string} fileName - 文件名
 */
export const downloadFile = (blob, fileName) => {
  if (!('download' in document.createElement('a'))) {
    console.error('浏览器不支持下载功能')
    return
  }

  const link = document.createElement('a')
  link.download = fileName
  link.style.display = 'none'
  link.href = URL.createObjectURL(blob)
  document.body.appendChild(link)
  link.click()
  URL.revokeObjectURL(link.href)
  document.body.removeChild(link)
}

/**
 * 将 JSON Blob 转换为 JavaScript 对象
 * @param {Blob} blob - 包含 JSON 数据的 Blob 对象
 * @returns {Promise<Object>} - 解析后的 JavaScript 对象
 */
export const blobToJson = async (blob) => {
  try {
    // 将 Blob 读取为文本
    const text = await blob.text()

    // 将文本解析为 JSON 对象
    const jsonData = JSON.parse(text)

    return jsonData
  } catch (error) {
    console.error('Blob 转 JSON 失败:', error)
    throw error
  }
}
