export class CancelControl {
  constructor() {
    this.cancelTokens = new Map()
  }

  setAbortAPI(apiKey) {
    this.abort(apiKey)
    const controller = new AbortController()
    this.cancelTokens.set(api<PERSON><PERSON>, controller)
    return controller.signal
  }

  abort(apiKey) {
    if (this.cancelTokens.has(apiKey)) {
      const controller = this.cancelTokens.get(apiKey)
      controller.abort()
      this.cancelTokens.delete(apiKey)
    }
  }

  abortAll() {
    this.cancelTokens.forEach((controller) => {
      controller.abort()
    })
    this.cancelTokens.clear()
  }
}

export const cancelControl = new CancelControl()
