<template>
  <div class="animated-list-container">
    <TransitionGroup
      v-bind="$attrs"
      ref="listRef"
      :name="animationName"
      :tag="tag"
      :class="wrapperClass"
    >
      <slot />
    </TransitionGroup>
  </div>
</template>

<script setup>
defineOptions({
  inheritAttrs: false
})

const listRef = ref(null)

defineProps({
  animationName: {
    type: String,
    default: 'list'
  },
  tag: {
    type: String,
    default: 'div'
  },
  wrapperClass: {
    type: String,
    default: 'animated-list-wrapper'
  }
})
</script>

<style lang="scss" scoped>
.animated-list-container {
  width: 100%;
  height: 100%;

  .animated-list-wrapper {
    @include flex(column, stretch, flex-start, 12px);
  }

  :deep(.task-list-wrapper) {
    @include flex(column, stretch, flex-start, 12px);
  }
}

:deep(.list-move),
:deep(.list-enter-active),
:deep(.list-leave-active) {
  transition: all 0.8s cubic-bezier(0.55, 0, 0.1, 1);
}

:deep(.list-enter-from),
:deep(.list-leave-to) {
  opacity: 0;
  transform: scaleY(0.01) translate(30px, 0);
}

:deep(.list-leave-active) {
  position: absolute;
  width: calc(100% - 12px);
}

:deep(.task-move),
:deep(.task-enter-active),
:deep(.task-leave-active) {
  transition: all 0.8s cubic-bezier(0.55, 0, 0.1, 1);
}

:deep(.task-enter-from),
:deep(.task-leave-to) {
  opacity: 0;
  transform: scaleY(0.01) translate(30px, 0);
}

:deep(.task-leave-active) {
  position: absolute;
}
</style>
