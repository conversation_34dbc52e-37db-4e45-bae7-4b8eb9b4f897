<template>
  <div ref="messageElement" class="message system-message">
    <div class="avatar">
      <img :src="ASSETS.ROBOT_AVATAR" alt="系统" />
      <span class="avatar-title">智能渗透测试系统</span>
    </div>
    <div class="message-content">
      <div class="message-text" v-html="displayText" />

      <LoadingIndicator v-if="showLoading" />

      <div v-if="isCompleted" class="document-preview" @click="$emit('preview-report')">
        <div class="document-icon">
          <img :src="ASSETS.WORD_ICON" alt="Word文档" />
        </div>
        <div class="document-info">
          <div class="document-name">{{ task.reportFileName }}</div>
        </div>
      </div>
    </div>

    <div
      v-if="showTerminateButton"
      class="terminate-button-wrapper"
      :style="{
        marginLeft:
          props.status === '1' && initialTaskSelectionType === 'NEW_TASK' ? '200px' : '145px'
      }"
    >
      <button class="terminate-btn" :disabled="terminateLoading" @click="handleTerminate">
        <el-icon v-if="terminateLoading" class="is-loading">
          <i-svg-spinners-3-dots-scale />
        </el-icon>
        <el-icon v-else>
          <i-majesticons:restricted-line />
        </el-icon>
        {{ terminateLoading ? '正在终止...' : '终止' }}
      </button>
    </div>
  </div>
</template>

<script setup>
import LoadingIndicator from '../common/LoadingIndicator.vue'
import robotAvatar from '@assets/img/robot.svg'
import wordIcon from '@assets/img/word.svg'
import { terminateTask } from '@api'
import { useTaskStore } from '@stores/taskStore'
import { TASK_STATUS } from '@utils/taskStatusUtils'
import { HOOK_STATE } from '@hooks/useTaskExecutionV2'

const ASSETS = {
  ROBOT_AVATAR: robotAvatar,
  WORD_ICON: wordIcon
}

const props = defineProps({
  task: { type: Object, required: true },
  status: { type: String, required: true },
  hookState: { type: String, default: '' },
  taskSelectionType: { type: String, default: '' },
  systemText: { type: String, default: '' },
  showLoading: { type: Boolean, default: false },
  isCompleted: { type: Boolean, default: false },
  hasStartedExecution: { type: Boolean, default: false }
})

const emit = defineEmits(['preview-report', 'task-terminated'])

const taskStore = useTaskStore()
const terminateLoading = ref(false)

const messageElement = ref(null)

const initialTaskSelectionType = ref(props.taskSelectionType)

watch(
  () => props.taskSelectionType,
  (newVal) => {
    if (newVal) {
      initialTaskSelectionType.value = newVal
    }
  },
  { immediate: true }
)

// 显示终止按钮的条件：任务状态是执行中且已经开始执行，但hooks状态不是已完成
const showTerminateButton = computed(() => {
  return (
    props.status === TASK_STATUS.RUNNING &&
    props.hasStartedExecution &&
    !props.isCompleted &&
    props.hookState !== HOOK_STATE.COMPLETED &&
    props.hookState !== HOOK_STATE.ERROR &&
    props.hookState !== HOOK_STATE.TERMINATED
  )
})

const displayText = computed(() => {
  if (props.status === TASK_STATUS.COMPLETED) {
    return (
      props.systemText ||
      `已完成关于${props.task.sysName}目标系统的任务执行，报告内容已经生成，请通过以下路径进行查看：`
    )
  }
  return props.systemText.replace(/\n/g, '<br>')
})

const handleTerminate = async () => {
  if (terminateLoading.value) return

  try {
    await ElMessageBox.confirm('确定要终止当前任务吗？终止后任务将无法恢复。', '终止确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    terminateLoading.value = true

    const result = await terminateTask(props.task.taskId)

    if (result.e) {
      ElMessage.error(result.m || '终止任务失败')
      return
    }
    taskStore.updateTaskStatus(props.task.taskId, TASK_STATUS.TERMINATED)
    emit('task-terminated')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('终止任务失败:', error)
      ElMessage.error('终止任务失败，请重试')
    }
  } finally {
    terminateLoading.value = false
  }
}

defineExpose({
  messageElement
})
</script>

<style lang="scss" scoped>
.message {
  @include message-base;

  &.system-message {
    @include system-message;

    .message-content {
      .document-preview {
        @include flex-start-center;

        width: fit-content;
        padding: 5px;
        cursor: pointer;
        background: #f7faff;
        border: 1px solid #3a87f4;
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          background: #e9ecef;
          border-color: #3b44fa;
          box-shadow: 0 2px 8px rgb(59 68 250 / 15%);
          transform: translateY(-1px);
        }

        .document-icon {
          width: 20px;
          height: 20px;
          margin-right: 10px;

          img {
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
        }

        .document-info {
          @include flex(column, flex-start, flex-start, 2px);

          .document-name {
            font-size: 14px;
            font-weight: 600;
            color: #3a87f4;
          }
        }
      }
    }

    .terminate-button-wrapper {
      @include flex(row, flex-end, flex-end);

      width: fit-content;
      margin-top: 8px;

      .terminate-btn {
        @include flex-start-center(8px);

        padding: 8px;
        font-size: 13px;
        font-weight: 500;
        color: #f56c6c;
        cursor: pointer;
        background: rgb(245 108 108 / 8%);
        border: 1px solid rgb(245 108 108 / 20%);
        border-radius: 8px;
        transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover:not(:disabled) {
          color: #fff;
          background: linear-gradient(135deg, #f56c6c 0%, #ea6078 100%);
          border-color: transparent;
          box-shadow: 0 4px 12px rgb(245 108 108 / 25%);
          transform: translateY(-1px);
        }

        &:active:not(:disabled) {
          box-shadow: 0 2px 6px rgb(245 108 108 / 20%);
          transform: translateY(0);
        }

        &:disabled {
          cursor: not-allowed;
          box-shadow: none;
          opacity: 0.5;
          transform: none;
        }

        .el-icon {
          font-size: 14px;

          &.is-loading {
            @include rotating-animation;
          }
        }
      }
    }
  }
}
</style>
