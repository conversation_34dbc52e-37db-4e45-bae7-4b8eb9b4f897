<template>
  <div v-if="loading" class="loading-tip">
    <!-- <i-svg-spinners-blocks-shuffle-3 class="loading-icon" /> -->
    <i-svg-spinners-3-dots-scale class="loading-icon" />
    <span>页面加载中...</span>
  </div>
  <div v-else class="task-execution">
    <!-- 对话区域 -->
    <div class="chat-container">
      <!-- 用户消息 -->
      <UserMessage ref="userMessageComponent" :task="task" />

      <!-- 系统消息 -->
      <SystemMessage
        v-if="shouldShowSystemMessage"
        ref="systemMessageComponent"
        :task="task"
        :status="task.status"
        :hook-state="hookState"
        :task-selection-type="taskStore.taskSelectionType"
        :system-text="systemDisplayText"
        :show-loading="showLoadingIcon"
        :is-completed="isCompleted"
        :has-started-execution="hasStartedExecution"
        @preview-report="handlePreviewReport"
        @task-terminated="handleTaskTerminated"
      />
    </div>

    <!-- 执行时间 -->
    <ExecutionTimer v-if="showExecutionTime" :format-duration="formattedDuration" />

    <!-- Word文档预览抽屉 -->
    <DocumentPreviewDrawer ref="documentPreviewDrawerRef" :task="task" />
  </div>
</template>

<script setup>
import { useTaskExecutionV2, useMessageAnimation } from '@hooks'
import { useTaskStore } from '@/stores/taskStore'
import UserMessage from './UserMessage.vue'
import SystemMessage from './SystemMessage.vue'
import ExecutionTimer from './ExecutionTimer.vue'
import DocumentPreviewDrawer from './DocumentPreviewDrawer.vue'

const props = defineProps({
  task: {
    type: Object,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  showDecimalTime: {
    type: Boolean,
    default: true
  }
})

defineEmits(['task-completed', 'task-failed', 'preview-opened'])

const taskStore = useTaskStore()

const {
  systemDisplayText,
  isCompleted,
  showLoadingIcon,
  formattedDuration,
  hasStartedExecution,
  hookState,
  initializeTaskExecution,
  clearTimers,
  resetState,
  handleTaskTermination
} = useTaskExecutionV2({ showDecimalTime: props.showDecimalTime })

const userMessageComponent = ref(null)
const systemMessageComponent = ref(null)

const {
  userMessageRef,
  systemMessageRef,
  animateUserMessage,
  animateSystemMessage,
  resetAnimation
} = useMessageAnimation()

const documentPreviewDrawerRef = useTemplateRef('documentPreviewDrawerRef')

const handlePreviewReport = () => {
  if (documentPreviewDrawerRef.value) {
    documentPreviewDrawerRef.value.drawerVisible = true
  }
}

const handleTaskTerminated = () => {
  handleTaskTermination()
}

const showExecutionTime = computed(() => {
  return props.task.status === '1' && !isCompleted.value
})

const shouldShowSystemMessage = computed(() => {
  return systemDisplayText.value || showLoadingIcon.value || isCompleted.value
})

const isNewTaskExecution = ref(false)

watch(shouldShowSystemMessage, async (newVal) => {
  if (newVal && isNewTaskExecution.value) {
    await nextTick()

    if (systemMessageComponent.value?.messageElement) {
      systemMessageRef.value = systemMessageComponent.value.messageElement
    }

    await animateSystemMessage(0.2)

    isNewTaskExecution.value = false
  }
})

const checkAndTriggerAnimation = async () => {
  const isNewTask = taskStore.taskSelectionType === 'NEW_TASK'

  if (isNewTask) {
    isNewTaskExecution.value = true
    resetAnimation()
    await nextTick()
    if (userMessageComponent.value?.messageElement) {
      userMessageRef.value = userMessageComponent.value.messageElement
    }
    await animateUserMessage()
  } else {
    isNewTaskExecution.value = false
  }

  await initializeTaskExecution(props.task)

  taskStore.clearTaskSelectionType()
}

watch(
  () => props.task.taskId,
  async (newTaskId, oldTaskId) => {
    if (newTaskId !== oldTaskId) {
      console.log(`任务切换: ${oldTaskId} -> ${newTaskId}`)
      clearTimers()
      resetState()
      resetAnimation()
      isNewTaskExecution.value = false

      if (documentPreviewDrawerRef.value) {
        documentPreviewDrawerRef.value.drawerVisible = false
      }

      await checkAndTriggerAnimation()
    }
  },
  { immediate: false }
)

onMounted(async () => {
  await checkAndTriggerAnimation()
})

onUnmounted(() => {
  clearTimers()
})
</script>

<style lang="scss" scoped>
.task-execution {
  @include flex(column);

  width: 100%;
  height: 100%;

  .chat-container {
    @include flex(column, stretch, flex-start, 25px);

    flex: 1;
    padding: 65px 60px 20px;
    overflow-y: auto;
  }
}

.loading-tip {
  @include loading-container;

  flex: 1;

  .loading-icon {
    font-size: 26px;
  }
}
</style>
