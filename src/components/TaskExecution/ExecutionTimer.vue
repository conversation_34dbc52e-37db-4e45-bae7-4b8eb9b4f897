<template>
  <div class="execution-time-footer">
    <div class="avatar">
      <img src="@/assets/img/robot.svg" alt="系统" />
    </div>
    <div class="time-container">
      <span class="status-text">正在运行中，耗时 {{ formatDuration }}</span>
      <i-svg-spinners-3-dots-scale-middle class="loading-icon" />
    </div>
  </div>
</template>

<script setup>
defineProps({
  formatDuration: {
    type: String,
    required: true
  }
})
</script>

<style lang="scss" scoped>
.execution-time-footer {
  @include glass-card(rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8));
  @include flex-center(row, 15px);

  width: fit-content;
  padding: 8px 20px;
  margin-right: auto;
  margin-left: auto;

  .avatar {
    font-size: 14px;
    font-weight: 600;
    color: #aa1c21;
    background: #fff;

    img {
      width: 36px;
      height: 36px;
    }
  }

  .time-container {
    @include flex(column, flex-start, flex-start, 5px);

    font-weight: 500;
    color: #303133;

    .status-text {
      font-size: 15px;
    }

    .loading-icon {
      font-size: 22px;
    }
  }
}
</style>
