<template>
  <div class="task-search">
    <div class="search-form">
      <el-form ref="searchFormRef" :model="searchForm" label-position="right">
        <el-form-item label="系统名称：" prop="sysName">
          <el-input v-model="searchForm.sysName" placeholder="请输入系统名称" clearable />
        </el-form-item>
        <el-form-item label="系统URL：" prop="sysUrl">
          <el-input v-model="searchForm.sysUrl" placeholder="请输入系统URL" clearable />
        </el-form-item>
        <el-form-item label="创建时间：" prop="createDate">
          <el-date-picker
            v-model="searchForm.createDate"
            type="date"
            placeholder="请选择创建日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            clearable
          />
        </el-form-item>
      </el-form>
    </div>

    <div class="search-actions">
      <el-button type="primary" @click="handleSearch">
        <el-icon> <i-ep-search /></el-icon>&nbsp;查询
      </el-button>
      <el-button @click="handleReset">
        <el-icon> <i-ep-refresh /></el-icon>&nbsp;重置
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { useVModel } from '@/hooks/useVModel'
const emit = defineEmits(['search', 'reset', 'update:modelValue'])

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

const searchForm = useVModel(props, 'modelValue', emit)
const searchFormRef = useTemplateRef('searchFormRef')
const handleSearch = () => {
  emit('search', searchForm.value)
}

const handleReset = () => {
  searchFormRef.value?.resetFields()
  emit('reset')
}
</script>

<style lang="scss" scoped>
.task-search {
  @include flex(column, stretch, flex-start, 10px);

  width: 100%;
  padding: 0 30px 0 10px;

  .search-form {
    :deep(.el-form-item) {
      @include form-item;

      .el-form-item__content {
        .el-input,
        .el-date-editor {
          width: 95%;

          .el-input__wrapper {
            @include input-wrapper;
          }

          .el-input__inner {
            @include input-inner;
          }
        }
      }
    }
  }

  .search-actions {
    @include flex(row, center, center, 16px);

    padding: 8px 0;

    .el-button {
      min-width: 80px;
      height: 40px;

      &.el-button--primary {
        @include gradient-button;
      }

      &:not(.el-button--primary) {
        @include normal-button;
      }
    }
  }
}
</style>
