<template>
  <div class="login-page">
    <div class="login-container">
      <div ref="headerRef" class="form-header">
        <img
          src="@/assets/img/robot-title.svg"
          alt="robot"
          class="robot-avatar"
          width="665"
          height="170"
        />
      </div>
      <div ref="formRef" class="form-content">
        <el-form
          ref="elFormRef"
          :model="form"
          :rules="rules"
          label-width="120px"
          class="centered-form"
        >
          <el-form-item label="账号：" prop="username">
            <el-input
              v-model="form.username"
              placeholder="默认账号：admin"
              @keyup.enter="handleLogin"
            />
          </el-form-item>
          <el-form-item label="密码：" prop="password">
            <el-input
              v-model="form.password"
              type="password"
              placeholder="默认密码：admin123"
              show-password
              @keyup.enter="handleLogin"
            />
          </el-form-item>
          <el-form-item class="button-form-item">
            <div class="action-btn-wrapper">
              <button
                class="login-btn"
                :disabled="loading"
                type="button"
                :style="{
                  padding: loading ? '8px 16px' : '8px 45px'
                }"
                @click="handleLogin"
              >
                <el-icon v-if="loading" class="is-loading">
                  <i-svg-spinners-3-dots-scale />
                </el-icon>
                {{ loading ? '登录中...' : '登录' }}
              </button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useAuthStore } from '@stores/authStore'
import { usePageAnimation } from '@hooks'

const router = useRouter()
const authStore = useAuthStore()
const { initStandardPageAnimation } = usePageAnimation()

const elFormRef = useTemplateRef('elFormRef')
const headerRef = useTemplateRef('headerRef')
const formRef = useTemplateRef('formRef')

const form = reactive({
  username: '',
  password: ''
})

const loading = ref(false)

const rules = {
  username: [{ required: true, message: '请输入账号', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
}

const handleLogin = async () => {
  if (loading.value) return

  const valid = await elFormRef.value.validate()
  if (!valid) {
    ElMessage.error('请完善登录信息')
    return
  }

  loading.value = true

  try {
    await new Promise((resolve) => setTimeout(resolve, 1500))

    if (form.username === 'admin' && form.password === 'admin123') {
      authStore.login({
        username: form.username,
        token: 'mock-token-' + Date.now()
      })

      ElMessage.success('登录成功')

      await router.push('/Dashboard')
    } else {
      ElMessage.error('账号或密码错误')
    }
  } catch (error) {
    console.error('登录失败:', error)
    ElMessage.error('登录失败，请重试')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  if (authStore.isLoggedIn) {
    router.push('/Dashboard')
    return
  }

  initStandardPageAnimation(headerRef, formRef)
})
</script>

<style lang="scss" scoped>
.login-page {
  @include flex(column, center, flex-start);

  width: 100%;
  height: 100vh;
  padding-top: 10vh;
  background: url('@/assets/img/background.svg') center/cover;

  .login-container {
    @include flex(column, center, center);

    .form-header {
      @include flex-center;

      // 初始状态隐藏，防止闪烁
      opacity: 0;
      transform: translateY(30px);

      .robot-avatar {
        width: auto;
        height: auto;
        object-fit: contain;
      }
    }

    .form-content {
      @include flex-center;

      width: 100%;
      padding: 40px 64px 28px 30px;
      background: #fff;
      border-radius: 30px;
      box-shadow: 0 4px 12px 4px rgb(128 128 128 / 15%);

      // 初始状态隐藏，防止闪烁
      opacity: 0;
      transform: translateY(30px);

      .centered-form {
        @include flex(column, center, flex-start, 15px);

        width: 100%;

        :deep(.el-form-item) {
          @include flex(row, center);

          .el-form-item__label {
            padding-right: 20px;
            font-size: 16px;
            font-weight: 400;
            color: #000;
            text-align: right;
          }

          .el-form-item__content {
            .el-input {
              width: 100%;

              .el-input__wrapper {
                @include input-wrapper(400px, 42px, #f6f6f6, transparent);

                outline: none !important;
                border: none !important;
                box-shadow: none !important;

                &:hover {
                  border: none !important;
                  box-shadow: none !important;
                }

                &.is-focus {
                  border: none !important;
                  box-shadow: none !important;
                }
              }

              .el-input__inner {
                @include input-inner(0 16px, 14px, #000);

                line-height: 24px;

                &:focus {
                  outline: none !important;
                  border: none !important;
                  box-shadow: none !important;
                }

                &::placeholder {
                  color: #94a0ad;
                }
              }
            }
          }

          &:last-child {
            margin-bottom: 0;
          }

          &.button-form-item {
            .el-form-item__content {
              margin-left: 0 !important;
            }
          }
        }
      }
    }
  }

  .action-btn-wrapper {
    @include flex(row, center, center);

    .login-btn {
      @include gradient-button(linear-gradient(101deg, #ea6078 0%, #be7aa0 64%, #9fcdf8 100%));
      @include flex-start-center(8px);

      margin-top: 15px;
      margin-left: 30px;
      font-size: 16px;
      color: #fff;
      border: unset;
      border-radius: 8px;

      .el-icon {
        font-size: 16px;
        @include rotating-animation;
      }
    }
  }
}
</style>
