/**
 * 任务执行状态枚举
 */
export const TASK_STATUS = {
  PENDING: '0', // 待执行
  RUNNING: '1', // 执行中
  COMPLETED: '9', // 已完成
  FAILED: '-1', // 执行失败
  TERMINATED: '-2' // 已终止
}

/**
 * 获取任务状态对应的显示文本
 * @param {string} status - 任务状态码
 * @param {string} taskName - 任务名称（可选）
 * @returns {string} 格式化后的状态信息
 */
export const getStatusMessage = (status, taskName = '目标系统') => {
  const messages = {
    [TASK_STATUS.PENDING]: '任务待执行',
    [TASK_STATUS.RUNNING]: '任务正在执行中，请稍后...',
    [TASK_STATUS.COMPLETED]: `已完成关于${taskName}目标系统的任务执行，报告内容已经生成，请通过以下路径进行查看：`,
    [TASK_STATUS.FAILED]: '任务执行失败，请重新创建任务',
    [TASK_STATUS.TERMINATED]: '任务已终止，请重新新建任务',
    default: '无法获取任务状态，请检查网络连接'
  }
  return messages[status] || messages.default
}

/**
 * 获取任务状态对应的显示类名
 * @param {string} status - 任务状态码
 * @returns {string} 对应的 CSS 类名
 */
export const getStatusClassName = (status) => {
  const classNames = {
    [TASK_STATUS.PENDING]: 'status-pending',
    [TASK_STATUS.RUNNING]: 'status-running',
    [TASK_STATUS.COMPLETED]: 'status-completed',
    [TASK_STATUS.FAILED]: 'status-failed',
    [TASK_STATUS.TERMINATED]: 'status-failed',
    default: 'status-unknown'
  }
  return classNames[status] || classNames.default
}

/**
 * 判断任务是否处于执行中状态
 * @param {string} status - 任务状态码
 * @returns {boolean} 是否执行中
 */
export const isTaskRunning = (status) => {
  return status === TASK_STATUS.RUNNING
}

/**
 * 判断任务是否已完成
 * @param {string} status - 任务状态码
 * @returns {boolean} 是否已完成
 */
export const isTaskCompleted = (status) => {
  return status === TASK_STATUS.COMPLETED
}

/**
 * 判断任务是否已失败
 * @param {string} status - 任务状态码
 * @returns {boolean} 是否已失败
 */
export const isTaskFailed = (status) => {
  return status === TASK_STATUS.FAILED
}

/**
 * 判断任务是否已终止
 * @param {string} status - 任务状态码
 * @returns {boolean} 是否已终止
 */
export const isTaskTerminated = (status) => {
  return status === TASK_STATUS.TERMINATED
}

/**
 * 获取可读的任务状态文本
 * @param {string} status - 任务状态码
 * @returns {string} 状态文本
 */
export const getStatusText = (status) => {
  const texts = {
    [TASK_STATUS.PENDING]: '待执行',
    [TASK_STATUS.RUNNING]: '执行中',
    [TASK_STATUS.COMPLETED]: '已完成',
    [TASK_STATUS.FAILED]: '执行失败',
    [TASK_STATUS.TERMINATED]: '已终止',
    default: '未知状态'
  }
  return texts[status] || texts.default
}
