import CryptoJS from 'crypto-js'

const ADD = '/add/'

const is = import.meta.env.VITE_BASE_Key

const aesUtil = () => {
  return '0e2ac1'.split('').reverse().join('')
}

export function encrypt(data) {
  const jsonString = JSON.stringify(data)

  let encrypted = CryptoJS.AES.encrypt(jsonString, CryptoJS.enc.Utf8.parse(is + aesUtil()), {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  }).toString()

  encrypted = encrypted.replace(/\+/g, ADD)
  return encrypted
}

export function decrypt(encryptedString) {
  let encryptedStr = encryptedString.replace(/\n/g, '')
  let decryptedString = encryptedStr.replace(new RegExp(ADD, 'g'), '+')

  let decrypted = CryptoJS.AES.decrypt(decryptedString, CryptoJS.enc.Utf8.parse(is + aesUtil()), {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  })

  return JSON.parse(decrypted.toString(CryptoJS.enc.Utf8))
}

export function urlDecrypt(word, keyStr) {
  keyStr = keyStr || 'lgXT@2.00.2@TXgl'
  let key = CryptoJS.enc.Utf8.parse(keyStr)
  let encryptedHexStr = CryptoJS.enc.Hex.parse(word)
  let srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr)
  let decrypt = CryptoJS.AES.decrypt(srcs, key, {
    iv: key,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  })
  return decrypt.toString(CryptoJS.enc.Utf8).toString()
}
