// 图片懒加载
export function lazyLoadImage(img, src) {
  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const image = entry.target
        image.src = src
        image.classList.remove('lazy')
        observer.unobserve(image)
      }
    })
  })

  observer.observe(img)
}

// 防抖函数
export function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 节流函数
export function throttle(func, limit) {
  let inThrottle
  return function (...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

// 预加载资源
export function preloadResource(href, as = 'script') {
  const link = document.createElement('link')
  link.rel = 'preload'
  link.href = href
  link.as = as
  document.head.appendChild(link)
}

// 动态导入组件
export function defineAsyncComponent(loader) {
  return () => ({
    component: loader(),
    loading: () => import('@/components/common/LoadingIndicator.vue'),
    delay: 200,
    timeout: 3000
  })
}

// 性能监控
export function performanceMonitor() {
  if (typeof window !== 'undefined' && window.performance) {
    const navigation = performance.getEntriesByType('navigation')[0]

    const safeCalculate = (end, start) => {
      if (!end || !start || end <= 0 || start <= 0) return 0
      const result = end - start
      return result >= 0 ? Math.round(result) : 0
    }

    return {
      // DNS 查询时间
      dns: safeCalculate(navigation.domainLookupEnd, navigation.domainLookupStart),
      // TCP 连接时间
      tcp: safeCalculate(navigation.connectEnd, navigation.connectStart),
      // 请求时间
      request: safeCalculate(navigation.responseEnd, navigation.requestStart),
      // 解析 DOM 树时间
      domParse: safeCalculate(navigation.domInteractive, navigation.responseEnd),
      // 白屏时间（首字节时间）
      whiteScreen: safeCalculate(navigation.responseStart, navigation.navigationStart),
      // 首屏时间（页面完全加载时间）
      firstScreen: safeCalculate(navigation.loadEventEnd, navigation.navigationStart),
      // 额外指标
      // DOM 内容加载完成时间
      domContentLoaded: safeCalculate(
        navigation.domContentLoadedEventEnd,
        navigation.navigationStart
      ),
      // DOM 解析完成时间
      domReady: safeCalculate(navigation.domComplete, navigation.navigationStart)
    }
  }
  return null
}
