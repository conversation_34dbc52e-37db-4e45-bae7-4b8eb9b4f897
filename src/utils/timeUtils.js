import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import relativeTime from 'dayjs/plugin/relativeTime'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import duration from 'dayjs/plugin/duration'
import customParseFormat from 'dayjs/plugin/customParseFormat'

// 配置 dayjs
dayjs.locale('zh-cn')
dayjs.extend(relativeTime)
dayjs.extend(isSameOrBefore)
dayjs.extend(isSameOrAfter)
dayjs.extend(duration)
dayjs.extend(customParseFormat)

/**
 * 格式化时间显示
 * @param {string|Date} time - 时间字符串或Date对象
 * @param {string} format - 格式化模板，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(time, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!time) return ''
  if (typeof time === 'string') {
    return dayjs(time, 'YYYY-MM-DD HH:mm:ss').format(format)
  }
  return dayjs(time).format(format)
}

/**
 * 格式化任务列表显示时间
 * @param {string|Date} time - 时间字符串或Date对象
 * @returns {string} 格式化后的时间字符串
 */
export function formatTaskTime(time) {
  if (!time) return ''
  if (typeof time === 'string') {
    return dayjs(time, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD HH:mm:ss')
  }
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 获取当前时间的标准格式
 * @returns {string} 当前时间的标准格式字符串
 */
export function getCurrentTime() {
  return dayjs().format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 计算任务执行时间（分钟）
 * @param {string|Date} startTime - 开始时间
 * @param {string|Date} endTime - 结束时间，默认为当前时间
 * @param {boolean} precise - 是否返回精确的小数分钟，默认为false
 * @returns {number} 执行时间（分钟）
 */
export function calculateExecutionTime(startTime, endTime = null, precise = false) {
  if (!startTime) return 0

  const start =
    typeof startTime === 'string' ? dayjs(startTime, 'YYYY-MM-DD HH:mm:ss') : dayjs(startTime)
  const end = endTime
    ? typeof endTime === 'string'
      ? dayjs(endTime, 'YYYY-MM-DD HH:mm:ss')
      : dayjs(endTime)
    : dayjs()

  if (precise) {
    // 返回精确的小数分钟（保留1位小数）
    const diffSeconds = end.diff(start, 'second')
    const diffMinutes = diffSeconds / 60
    return Math.max(0, Math.round(diffMinutes * 10) / 10)
  } else {
    // 返回整数分钟
    const diffMinutes = end.diff(start, 'minute')
    return Math.max(0, diffMinutes)
  }
}

/**
 * 格式化执行时间显示
 * @param {number} minutes - 分钟数
 * @param {boolean} showDecimal - 是否显示小数分钟，默认为false
 * @returns {string} 格式化后的时间显示
 */
export function formatDuration(minutes, showDecimal = false) {
  // if (minutes < 0.1) return '不到0.1分钟'

  if (showDecimal && minutes < 60) {
    // 小于1小时时，显示小数分钟
    return `${minutes} 分钟`
  }

  if (minutes < 1) return '0 分钟'

  const hours = Math.floor(minutes / 60)
  const remainingMinutes = Math.floor(minutes % 60)

  if (hours > 0) {
    return remainingMinutes > 0 ? `${hours} 小时 ${remainingMinutes} 分钟` : `${hours} 小时`
  }

  return `${Math.floor(minutes)} 分钟`
}

/**
 * 格式化相对时间（多久前）
 * @param {string|Date} time - 时间字符串或Date对象
 * @returns {string} 相对时间字符串
 */
export function formatRelativeTime(time) {
  if (!time) return ''
  if (typeof time === 'string') {
    return dayjs(time, 'YYYY-MM-DD HH:mm:ss').fromNow()
  }
  return dayjs(time).fromNow()
}

/**
 * 判断时间是否为今天
 * @param {string|Date} time - 时间字符串或Date对象
 * @returns {boolean} 是否为今天
 */
export function isToday(time) {
  if (!time) return false
  const d = typeof time === 'string' ? dayjs(time, 'YYYY-MM-DD HH:mm:ss') : dayjs(time)
  return d.isSame(dayjs(), 'day')
}

/**
 * 判断时间是否为本周
 * @param {string|Date} time - 时间字符串或Date对象
 * @returns {boolean} 是否为本周
 */
export function isThisWeek(time) {
  if (!time) return false
  const d = typeof time === 'string' ? dayjs(time, 'YYYY-MM-DD HH:mm:ss') : dayjs(time)
  return d.isSame(dayjs(), 'week')
}

/**
 * 获取友好的时间显示
 * 今天显示时间，昨天显示"昨天"，更早显示日期
 * @param {string|Date} time - 时间字符串或Date对象
 * @returns {string} 友好的时间显示
 */
export function getFriendlyTime(time) {
  if (!time) return ''
  const dayjsTime = typeof time === 'string' ? dayjs(time, 'YYYY-MM-DD HH:mm:ss') : dayjs(time)
  const now = dayjs()
  if (dayjsTime.isSame(now, 'day')) {
    // 今天：显示时间
    return '今天' + ' ' + dayjsTime.format('HH:mm')
  } else if (dayjsTime.isSame(now.subtract(1, 'day'), 'day')) {
    // 昨天
    return '昨天 ' + dayjsTime.format('HH:mm')
  } else if (dayjsTime.isSame(now, 'year')) {
    // 今年：显示月日
    return dayjsTime.format('MM-DD HH:mm')
  } else {
    return dayjsTime.format('YYYY-MM-DD HH:mm:ss')
  }
}

/**
 * 验证时间字符串是否有效
 * @param {string} timeString - 时间字符串
 * @returns {boolean} 是否有效
 */
export function isValidTime(timeString) {
  return dayjs(timeString, 'YYYY-MM-DD HH:mm:ss', true).isValid()
}

/**
 * 时间范围格式化
 * @param {string|Date} startTime - 开始时间
 * @param {string|Date} endTime - 结束时间
 * @returns {string} 时间范围字符串
 */
export function formatTimeRange(startTime, endTime) {
  if (!startTime) return ''

  const start =
    typeof startTime === 'string' ? dayjs(startTime, 'YYYY-MM-DD HH:mm:ss') : dayjs(startTime)
  const end = endTime
    ? typeof endTime === 'string'
      ? dayjs(endTime, 'YYYY-MM-DD HH:mm:ss')
      : dayjs(endTime)
    : dayjs()

  if (start.isSame(end, 'day')) {
    // 同一天
    return `${start.format('YYYY-MM-DD')} ${start.format('HH:mm')} - ${end.format('HH:mm')}`
  } else {
    // 不同天
    return `${start.format('YYYY-MM-DD HH:mm')} - ${end.format('YYYY-MM-DD HH:mm')}`
  }
}
