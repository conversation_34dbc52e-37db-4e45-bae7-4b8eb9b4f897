* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  scrollbar-color: rgb(0 0 0 / 20%) transparent;
  scrollbar-width: thin;
}

ul,
ol {
  list-style: none;
}

a {
  text-decoration: none;
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: transparent; /* 轨道透明 */
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgb(0 0 0 / 20%);
  border-radius: 4px;
  transition: background 0.3s ease;

  &:hover {
    background: rgb(0 0 0 / 30%);
  }

  &:active {
    background: rgb(0 0 0 / 40%);
  }
}

::-webkit-scrollbar-corner {
  background: transparent;
}

/* 针对深色背景的滚动条样式 */
.dark-scrollbar {
  ::-webkit-scrollbar-thumb {
    background: rgb(255 255 255 / 30%);

    &:hover {
      background: rgb(255 255 255 / 40%);
    }

    &:active {
      background: rgb(255 255 255 / 50%);
    }
  }

  scrollbar-color: rgb(255 255 255 / 30%) transparent;
}

.user-dropdown-popper {
  /* 整体下拉菜单容器样式 */
  .el-dropdown-menu {
    padding: 0 !important;
  }

  /* 菜单项样式 */
  .el-dropdown-menu__item {
    margin: 2px !important;
    border-radius: 12px !important;
    transition: all 0.2s ease !important;

    &:not(.is-disabled):hover,
    &:not(.is-disabled):focus {
      color: #ea6078;
      background: transparent;

      .el-icon {
        color: #ea6078 !important;
      }
    }
  }
}
