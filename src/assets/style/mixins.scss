// ===== Flex 布局混合 =====

// 基础 flex 布局
@mixin flex($direction: row, $align: stretch, $justify: flex-start, $gap: 0) {
  display: flex;
  flex-direction: $direction;
  align-items: $align;
  justify-content: $justify;

  @if $gap != 0 {
    gap: $gap;
  }
}

// 居中布局
@mixin flex-center($direction: row, $gap: 0) {
  @include flex($direction, center, center, $gap);
}

// 两端对齐
@mixin flex-between($align: center, $gap: 0) {
  @include flex(row, $align, space-between, $gap);
}

// 垂直居中，水平左对齐
@mixin flex-start-center($gap: 0) {
  @include flex(row, center, flex-start, $gap);
}

// ===== 输入框样式混合 =====

@mixin input-wrapper(
  $width: 280px,
  $height: 38px,
  $bg: #fff,
  $border-color: #e7ecf1bd,
  $radius: 8px
) {
  width: $width;
  height: $height;
  background: $bg;
  border: 1px solid $border-color;
  border-radius: $radius;

  // &:hover {
  //   border-color: darken($border-color, 10%);
  // }

  &.is-focus {
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgb(64 158 255 / 20%);
  }
}

// 输入框内部样式
@mixin input-inner($padding: 0 16px, $font-size: 14px, $color: #778697) {
  padding: $padding;
  font-size: $font-size;
  line-height: 22px;
  color: $color;
  outline: none !important;
  border: none !important;

  &:focus {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
  }

  &::placeholder {
    font-weight: 400;
    color: $color;
  }
}

// 表单项样式
@mixin form-item($label-width: 110px, $label-color: #2d3e4f) {
  @include flex(row, center, space-evenly);

  margin-bottom: 10px;

  .el-form-item__label {
    min-width: $label-width;
    font-size: 15px;
    font-weight: 400;
    color: $label-color;
    letter-spacing: 0;
  }

  .el-form-item__content {
    flex: 1;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

// ===== 按钮样式混合 =====

// 基础按钮样式
@mixin button-base($padding: 12px 20px, $font-size: 15px, $radius: 10px) {
  position: relative;
  box-sizing: border-box;
  padding: $padding;
  overflow: hidden;
  font-size: $font-size;
  font-weight: 500;
  cursor: pointer;
  border: 2px solid #ffffffbd;
  border-radius: $radius;

  .el-icon {
    margin-right: 6px;
    font-size: 16px;
  }
}

// 渐变按钮
@mixin gradient-button($gradient: linear-gradient(100deg, #ea6078 0%, #be7aa0 64%, #9fcdf8 100%)) {
  @include button-base;

  color: #fff;
  background-image: $gradient;

  &:hover:not(:disabled) {
    box-shadow: 0 4px 12px rgb(234 96 120 / 25%);
  }

  &:disabled {
    cursor: not-allowed;
    box-shadow: none;
    opacity: 0.6;
    transform: none;
  }
}

// 普通按钮
@mixin normal-button($bg: #e0e0e0, $color: #333) {
  @include button-base;

  color: $color;
  background: $bg;

  // &:hover {
  //   background: darken($bg, 5%);
  // }
}

// ===== 卡片/容器样式混合 =====

// 基础卡片样式
@mixin card(
  $bg: #fff,
  $radius: 16px,
  $shadow: 0 1px 3px rgba(0, 0, 0, 0.04),
  $border: 1px solid #f0f2f5
) {
  background: $bg;
  border: $border;
  border-radius: $radius;
  box-shadow: $shadow;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

// 毛玻璃效果卡片
@mixin glass-card($bg: rgba(255, 255, 255, 0.95), $border: rgba(255, 255, 255, 0.8)) {
  background: $bg;
  border: 1px solid $border;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  backdrop-filter: blur(10px);
}

// 悬停效果卡片
@mixin hover-card() {
  @include card;

  &:hover {
    box-shadow:
      0 2px 8px rgb(0 0 0 / 5%),
      0 0 0 1px rgb(255 182 193 / 6%);
    transform: translateY(-1px);
  }
}

// ===== 状态样式混合 =====

// 状态点
@mixin status-dot($size: 10px) {
  flex-shrink: 0;
  width: $size;
  height: $size;
  border-radius: 50%;

  &.status-completed {
    background-image: linear-gradient(107deg, #3b44fa 2%, #bcc5ff 85%);
  }

  &.status-running {
    background-image: linear-gradient(107deg, #ff6b35 2%, #ffb366 85%);
  }

  &.status-pending {
    background-image: linear-gradient(107deg, #909399 2%, #c0c4cc 85%);
  }

  &.status-failed {
    background-image: linear-gradient(107deg, #f56c6c 2%, #fbc4ab 85%);
  }

  &.status-terminated {
    background-image: linear-gradient(107deg, #8b5a3c 2%, #d4a574 85%);
  }
}

// ===== 加载状态混合 =====

// 加载提示容器
@mixin loading-container($bg: rgba(255, 255, 255, 0.5), $color: #ea6078) {
  @include flex-center(column, 10px);

  padding: 40px 20px;
  color: $color;
  background: $bg;
  border: 1px solid rgb(255 255 255 / 60%);
  border-top: none;
  border-radius: 0 0 16px 16px;
  backdrop-filter: blur(10px);

  .loading-icon {
    font-size: 24px;
  }

  span {
    margin-left: 12px;
    font-size: 16px;
    font-weight: 500;
  }
}

// ===== 消息样式混合 =====

// 基础消息容器
@mixin message-base() {
  @include flex(row);

  width: 100%;
  transform-origin: bottom center;
  will-change: transform, opacity;
}

// 用户消息样式
@mixin user-message($bg: #ebedf9, $color: #333) {
  justify-content: flex-end;

  .message-content {
    width: fit-content;
    padding: 15px 20px;
    color: $color;
    background: $bg;
    border-radius: 12px;

    .message-text {
      @include flex(column, flex-start, flex-start, 15px);

      font-size: 16px;
    }

    .message-time {
      padding-top: 10px;
      font-size: 14px;
      color: #a8a8a8;
      text-align: end;
    }
  }
}

// 系统消息样式
@mixin system-message($bg: #fff, $color: #303133) {
  flex-direction: column;

  .avatar {
    @include flex-start-center(10px);

    flex-shrink: 0;
    width: fit-content;
    padding: 0 15px;
    font-size: 14px;
    font-weight: 600;
    color: #aa1c21;
    background: #fff;

    img {
      width: 36px;
      height: 36px;
    }
  }

  .message-content {
    @include flex(column, flex-start, flex-start, 10px);

    width: fit-content;
    padding: 2px 20px 10px;
    color: $color;
    background: $bg;
    border-radius: 12px;

    .message-text {
      font-size: 16px;
      font-weight: 400;
      word-wrap: break-word;
    }
  }
}

// ===== 动画混合 =====

// 旋转动画
@mixin rotating-animation() {
  @keyframes rotating {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  &.is-loading {
    animation: rotating 2s linear infinite;
  }
}

// 淡入淡出动画
@mixin fade-transition($duration: 0.3s) {
  transition: all $duration cubic-bezier(0.25, 0.8, 0.25, 1);

  &-enter-from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }

  &-leave-to {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }

  &-enter-to,
  &-leave-from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
