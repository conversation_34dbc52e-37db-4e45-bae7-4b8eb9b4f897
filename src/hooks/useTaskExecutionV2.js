import { useTaskStore } from '@stores/taskStore'
import {
  calculateExecutionTime,
  formatDuration as formatDurationUtil,
  getCurrentTime
} from '@/utils/timeUtils'
import { useTypeWriter, useInterval } from '@/hooks'
import { startTaskExecution, getTaskDetail } from '@/api'
import { TASK_STATUS, getStatusMessage } from '@/utils/taskStatusUtils'

/**
 * Hook 内部状态
 */
export const HOOK_STATE = {
  IDLE: 'idle', // 空闲状态
  INITIALIZING: 'initializing', // 初始化中
  EXECUTING: 'executing', // 执行中
  MONITORING: 'monitoring', // 监控中
  COMPLETED: 'completed', // 已完成
  ERROR: 'error', // 错误状态
  TERMINATED: 'terminated' // 已终止
}

/**
 * 配置常量
 */
const CONFIG = {
  SWITCH_TASK_CHECK_INTERVAL: 15000, // 切换任务状态检查间隔（毫秒）
  NEW_TASK_CHECK_INTERVAL: 30000, // 新建任务状态检查间隔（毫秒）
  TIME_UPDATE_INTERVAL: 1000 // 时间更新间隔（毫秒）
}

/**
 * 负责管理整个渗透测试任务的生命周期：创建、执行、监控、完成
 *
 * @param {Object} options - 配置选项
 * @param {boolean} options.showDecimalTime - 是否显示小数时间（默认 false）
 * @returns {Object} Hook 返回的响应式数据和方法
 */
export function useTaskExecutionV2(options = {}) {
  const { showDecimalTime = false } = options

  const taskStore = useTaskStore()

  const hookState = ref(HOOK_STATE.IDLE)
  const systemDisplayText = ref('')
  const isCompleted = ref(false)
  const showLoadingIcon = ref(false)
  const executionTime = ref(0)
  const currentTask = ref(null)
  const hasStartedExecution = ref(false)
  const simulationController = ref(null)
  const isInitializing = ref(false)

  const { typeWriterQueue } = useTypeWriter(systemDisplayText, simulationController)

  // 定时器管理
  let timeTrackingInterval = null
  let statusCheckTimer = null

  const isLoading = computed(() => showLoadingIcon.value)
  const isExecuting = computed(
    () => hookState.value === HOOK_STATE.EXECUTING || hookState.value === HOOK_STATE.MONITORING
  )
  const hasError = computed(() => hookState.value === HOOK_STATE.ERROR)
  const formattedDuration = computed(() => formatDurationUtil(executionTime.value, showDecimalTime))

  /**
   * 状态管理方法
   */
  const setState = (newState, payload = {}) => {
    hookState.value = newState
    if (payload.task) {
      currentTask.value = payload.task
    }
  }

  /**
   * 清理所有定时器
   */
  const clearAllTimers = () => {
    if (timeTrackingInterval) {
      timeTrackingInterval.stop()
      timeTrackingInterval = null
    }
    if (statusCheckTimer) {
      statusCheckTimer.stop()
      statusCheckTimer = null
    }
  }

  /**
   * 清理所有资源
   */
  const cleanup = () => {
    clearAllTimers()

    // 清理控制器
    if (simulationController.value) {
      simulationController.value.cancelled = true
      simulationController.value = null
    }
  }

  /**
   * 更新执行时间
   */
  const updateExecutionTime = (startDate, endDate = null) => {
    executionTime.value = calculateExecutionTime(startDate, endDate, showDecimalTime)
  }

  /**
   * 开始时间跟踪
   */
  const startTimeTracking = (startDate) => {
    if (timeTrackingInterval) {
      timeTrackingInterval.stop()
      timeTrackingInterval = null
    }

    updateExecutionTime(startDate)

    timeTrackingInterval = useInterval(() => {
      if (hookState.value === HOOK_STATE.COMPLETED) {
        if (timeTrackingInterval) {
          timeTrackingInterval.stop()
          timeTrackingInterval = null
        }
        return
      }
      updateExecutionTime(startDate)
    }, CONFIG.TIME_UPDATE_INTERVAL)

    timeTrackingInterval.start()
  }

  /**
   * 统一的任务状态设置方法
   */
  const setTaskStatus = async (status, task, options = {}) => {
    const { useTypeWriter = true, updateTaskList = true, detail = {}, showIcon = false } = options

    setState(status, { task })
    showLoadingIcon.value = false
    clearAllTimers()

    // 更新最终执行时间（如果是完成状态）
    if (status === HOOK_STATE.COMPLETED) {
      const endDate = detail.endDate || getCurrentTime()
      updateExecutionTime(task.startDate, endDate)
    }

    // 显示状态信息
    const message = getStatusMessage(
      status === HOOK_STATE.COMPLETED
        ? TASK_STATUS.COMPLETED
        : status === HOOK_STATE.ERROR
          ? TASK_STATUS.FAILED
          : status === HOOK_STATE.TERMINATED
            ? TASK_STATUS.TERMINATED
            : status,
      task?.sysName
    )

    if (useTypeWriter) {
      await typeWriterQueue(message)
      // 打字机效果完成后再显示图标
      if (status === HOOK_STATE.COMPLETED && showIcon) {
        isCompleted.value = true
      }
    } else {
      systemDisplayText.value = message
      // 直接显示时立即显示图标
      if (status === HOOK_STATE.COMPLETED && showIcon) {
        isCompleted.value = true
      }
    }

    // 更新任务列表状态
    if (updateTaskList && status !== HOOK_STATE.TERMINATED) {
      const taskStatus =
        status === HOOK_STATE.COMPLETED
          ? TASK_STATUS.COMPLETED
          : status === HOOK_STATE.ERROR
            ? TASK_STATUS.FAILED
            : null

      if (taskStatus) {
        taskStore.updateTaskStatus(task.taskId, taskStatus, {
          endDate: detail.endDate || getCurrentTime(),
          reportFileName: detail.reportFileName
        })
      }
    }
  }

  /**
   * 标记任务为完成状态
   */
  const markTaskCompleted = async (
    task,
    detail = {},
    useTypeWriter = true,
    updateTaskList = true
  ) => {
    await setTaskStatus(HOOK_STATE.COMPLETED, task, {
      useTypeWriter,
      updateTaskList,
      detail,
      showIcon: true
    })
  }

  /**
   * 标记任务为失败状态
   */
  const markTaskFailed = async (task, useTypeWriter = true, updateTaskList = true) => {
    await setTaskStatus(HOOK_STATE.ERROR, task, {
      useTypeWriter,
      updateTaskList
    })
  }

  /**
   * 处理任务终止
   */
  const handleTaskTermination = async () => {
    console.log('🛑 处理任务终止')
    cleanup()
    setState(HOOK_STATE.TERMINATED)
    showLoadingIcon.value = false
    const terminatedMessage = getStatusMessage(TASK_STATUS.TERMINATED)
    await typeWriterQueue(terminatedMessage)
  }

  /**
   * 统一的任务状态检查方法
   */
  const checkTaskStatus = async (task, options = {}) => {
    const { isInitialCheck = true, silent = false, showTypeWriterOnComplete = true } = options

    console.log('🔍 checkTaskStatus被调用:', {
      taskId: task.taskId,
      taskName: task.sysName,
      isInitialCheck,
      silent
    })

    try {
      if (!silent && isInitialCheck) {
        // 只在初始检查时显示获取详情的提示
        systemDisplayText.value = '正在获取任务详情...'
        showLoadingIcon.value = true
      }

      const { d: detail } = await getTaskDetail(task.taskId)

      if (!detail) {
        throw new Error('无法获取任务详情')
      }

      console.log('获取任务详情:', {
        taskId: task.taskId,
        status: detail.status,
        isInitialCheck,
        silent
      })

      switch (detail.status) {
        case TASK_STATUS.COMPLETED: {
          const shouldUpdateTaskList = task.status !== detail.status
          console.log('任务完成状态检查:', {
            taskId: task.taskId,
            oldStatus: task.status,
            newStatus: detail.status,
            shouldUpdate: shouldUpdateTaskList,
            showTypeWriterOnComplete
          })
          await markTaskCompleted(task, detail, showTypeWriterOnComplete, shouldUpdateTaskList)
          break
        }

        case TASK_STATUS.RUNNING:
          console.log('⚠️ 检测到RUNNING状态:', {
            taskId: task.taskId,
            taskName: task.sysName,
            isInitialCheck,
            silent
          })

          if (isInitialCheck && !silent) {
            // 直接显示执行中状态，不显示"正在检查任务状态"
            showLoadingIcon.value = true
            systemDisplayText.value = '任务正在执行中，请稍后...'
            setState(HOOK_STATE.MONITORING, { task })
            startTimeTracking(task.startDate)
            // 切换到执行中的任务时，认为已经开始执行，可以显示终止按钮
            hasStartedExecution.value = true
            startTaskMonitoring(task, { interval: CONFIG.SWITCH_TASK_CHECK_INTERVAL })
          } else if (!silent) {
            showLoadingIcon.value = true
          }
          break

        case TASK_STATUS.FAILED: {
          const shouldUpdateTaskList = task.status !== detail.status
          console.log('任务失败状态检查:', {
            taskId: task.taskId,
            oldStatus: task.status,
            newStatus: detail.status,
            shouldUpdate: shouldUpdateTaskList
          })
          await markTaskFailed(task, !silent, shouldUpdateTaskList)
          break
        }

        default:
          throw new Error(`未知的任务状态: ${detail.status}`)
      }
    } catch (error) {
      console.error('任务状态检查失败:', error)

      if (!isInitialCheck && silent) {
        console.log('静默检查失败，将在下次继续尝试:', error.message)
        return
      }

      if (isInitialCheck) {
        setState(HOOK_STATE.ERROR)
        showLoadingIcon.value = false
        systemDisplayText.value = '操作失败，请检查网络连接或重新尝试'
      }
    }
  }

  /**
   * 统一的任务监控方法
   */
  const startTaskMonitoring = (task, options = {}) => {
    const {
      interval = CONFIG.SWITCH_TASK_CHECK_INTERVAL,
      silent = false,
      showTypeWriterOnComplete = true
    } = options

    console.log('🔍 启动任务监控:', {
      taskId: task.taskId,
      taskName: task.sysName,
      interval,
      silent,
      showTypeWriterOnComplete
    })

    // 清理现有定时器
    clearAllTimers()

    statusCheckTimer = useInterval(() => {
      // 检查当前状态是否仍需要监控
      if (hookState.value !== HOOK_STATE.MONITORING) return

      // 从 taskStore 获取当前任务，避免闭包引用问题
      const currentTask = taskStore.currentTask
      if (!currentTask || !currentTask.taskId) {
        console.log('🔍 定期检查跳过：没有当前任务')
        return
      }

      console.log('🔍 定期检查任务状态...', {
        originalTaskId: task.taskId,
        currentTaskId: currentTask.taskId,
        taskMatches: task.taskId === currentTask.taskId
      })

      // 只有当定时器的任务与当前任务匹配时才执行检查
      if (task.taskId === currentTask.taskId) {
        checkTaskStatus(currentTask, {
          isInitialCheck: false,
          silent,
          showTypeWriterOnComplete
        })
      } else {
        console.log('🔍 定期检查跳过：任务不匹配，停止定时器')
        statusCheckTimer.stop()
        statusCheckTimer = null
      }
    }, interval)

    // 启动定时器
    statusCheckTimer.start()
    console.log('🔍 任务监控定时器已启动，间隔:', interval, 'ms')
  }

  /**
   * 新任务执行流程
   */
  const executeNewTask = async (task) => {
    try {
      console.log('🚀 executeNewTask 开始执行:', {
        taskId: task.taskId,
        taskName: task.sysName,
        taskStatus: task.status
      })

      setState(HOOK_STATE.EXECUTING, { task })
      cleanup()

      const controller = { cancelled: false }
      simulationController.value = controller

      console.log('🚀 准备显示打字机效果...')
      await typeWriterQueue('已接收到任务，开始执行，请稍后...')
      console.log('🚀 打字机效果完成')

      if (controller.cancelled) return

      showLoadingIcon.value = true
      startTimeTracking(task.startDate)

      // 调用开始执行接口
      const result = await startTaskExecution(task.taskId)
      console.log('执行任务接口返回成功:', result.d)
      if (!result && !result.d) throw new Error(result.m)

      if (controller.cancelled) return

      // 标记已开始执行，此时可以显示终止按钮
      hasStartedExecution.value = true

      // 切换到监控状态，开始轮询
      setState(HOOK_STATE.MONITORING, { task })

      // 开始新建任务的轮询监控（1分钟间隔，静默检查但完成时显示打字机效果）
      console.log('🚀 准备启动新建任务轮询监控')
      startTaskMonitoring(task, {
        interval: CONFIG.NEW_TASK_CHECK_INTERVAL,
        silent: true, // 轮询过程中保持静默
        showTypeWriterOnComplete: true // 但完成时显示打字机效果
      })

      console.log('🚀 新建任务已启动，开始轮询监控状态')
    } catch (error) {
      console.error('启动新建任务失败:', error)
      setState(HOOK_STATE.ERROR, { task })
      showLoadingIcon.value = false
      systemDisplayText.value = getStatusMessage(TASK_STATUS.FAILED)
      taskStore.updateTaskStatus(task.taskId, TASK_STATUS.FAILED)
      taskStore.clearTaskSelectionType()
    } finally {
      simulationController.value = null
    }
  }

  /**
   * 初始化任务执行
   */
  const initializeTaskExecution = async (task) => {
    if (!task || !task.taskId) {
      throw new Error('无效的任务数据')
    }

    // 防止并发初始化
    if (isInitializing.value) {
      return
    }

    // 判断是否为新建任务
    const isNewTask = taskStore.taskSelectionType === 'NEW_TASK'

    // 防止重复初始化同一个任务
    if (currentTask.value?.taskId === task.taskId && !isNewTask) {
      return
    }

    try {
      isInitializing.value = true
      setState(HOOK_STATE.INITIALIZING, { task })

      // 只有在必要时才清理资源
      const needsCleanup =
        isNewTask ||
        task.status === TASK_STATUS.RUNNING ||
        (currentTask.value && currentTask.value.status === TASK_STATUS.RUNNING)

      if (needsCleanup) {
        cleanup()
      } else {
        // 对于静态任务（已完成、失败、终止），只清理打字机效果
        if (simulationController.value) {
          simulationController.value.cancelled = true
          simulationController.value = null
        }
      }

      if (task.status === TASK_STATUS.RUNNING) {
        updateExecutionTime(task.startDate, task.endDate)
      }

      switch (task.status) {
        case TASK_STATUS.COMPLETED: {
          // 切换到已完成的任务 - 直接显示，不使用打字机效果
          await setTaskStatus(HOOK_STATE.COMPLETED, task, {
            useTypeWriter: false,
            updateTaskList: false,
            showIcon: true
          })
          break
        }

        case TASK_STATUS.FAILED: {
          await setTaskStatus(HOOK_STATE.ERROR, task, {
            useTypeWriter: isNewTask,
            updateTaskList: false
          })
          break
        }

        case TASK_STATUS.TERMINATED: {
          await setTaskStatus(HOOK_STATE.TERMINATED, task, {
            useTypeWriter: isNewTask,
            updateTaskList: false
          })
          break
        }

        case TASK_STATUS.RUNNING: {
          if (isNewTask) {
            await executeNewTask(task)
          } else {
            await checkTaskStatus(task, true)
          }
          break
        }

        default:
          throw new Error(`未知的任务状态: ${task.status}`)
      }
    } catch (error) {
      setState(HOOK_STATE.ERROR)
      showLoadingIcon.value = false
      systemDisplayText.value = '操作失败，请检查网络连接或重新尝试'
      console.error('初始化任务执行失败:', error)
    } finally {
      isInitializing.value = false
    }
  }

  /**
   * 重置状态
   */
  const resetState = () => {
    cleanup()
    setState(HOOK_STATE.IDLE)
    systemDisplayText.value = ''
    isCompleted.value = false
    showLoadingIcon.value = false
    executionTime.value = 0
    currentTask.value = null
    hasStartedExecution.value = false
  }

  /**
   * 取消当前操作
   */
  const cancel = () => {
    cleanup()
    setState(HOOK_STATE.IDLE)
    showLoadingIcon.value = false
    systemDisplayText.value = '操作已取消'
  }

  const instance = getCurrentInstance()
  if (instance) {
    onUnmounted(() => {
      cleanup()
    })
  }

  return {
    systemDisplayText,
    isCompleted,
    showLoadingIcon,
    executionTime,
    hasStartedExecution,

    hookState: computed(() => hookState.value),
    currentTask: computed(() => currentTask.value),

    isLoading,
    isExecuting,
    hasError,
    formattedDuration,

    initializeTaskExecution,
    resetState,
    cancel,
    clearTimers: cleanup,
    handleTaskTermination
  }
}
