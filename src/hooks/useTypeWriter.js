/**
 * 打字机效果
 * @param {Ref} displayTextRef - 用于显示文本的 ref
 * @param {Ref} controllerRef - 控制打字机中断的 ref
 * @returns {Object} { typeWriterQueue }
 */
export function useTypeWriter(displayTextRef, controllerRef) {
  const typeWriterQueue = async (text, delay = 50) => {
    displayTextRef.value = ''
    for (let i = 0; i < text.length; i++) {
      if (controllerRef.value?.cancelled) {
        return
      }
      displayTextRef.value += text[i]
      await new Promise((resolve) => setTimeout(resolve, delay))
    }
  }

  return {
    typeWriterQueue
  }
}
