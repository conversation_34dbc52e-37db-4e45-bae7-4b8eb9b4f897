/**
 * 管理延时执行的 Hook，提供设置、清理、重置功能
 *
 * @param {Function} callback - 要延时执行的回调函数
 * @param {number} delay - 延迟时间（毫秒）
 * @param {Object} options - 配置选项
 * @param {boolean} options.immediate - 是否立即启动定时器（默认 false）
 * @param {boolean} options.cleanupOnUnmount - 是否在组件卸载时自动清理（默认 true）
 * @returns {Object} 定时器控制对象
 */
export function useTimeout(callback, delay = 1000, options = {}) {
  const { immediate = false, cleanupOnUnmount = true } = options

  const timer = ref(null)
  const isActive = ref(false)

  /**
   * 设置延时执行
   */
  const start = () => {
    if (timer.value) {
      clearTimeout(timer.value)
    }

    isActive.value = true
    timer.value = setTimeout(() => {
      callback()
      isActive.value = false
      timer.value = null
    }, delay)
  }

  /**
   * 取消延时执行
   */
  const cancel = () => {
    if (timer.value) {
      clearTimeout(timer.value)
      timer.value = null
      isActive.value = false
    }
  }

  /**
   * 重置延时执行（取消后重新开始）
   */
  const reset = () => {
    cancel()
    start()
  }

  if (immediate) {
    start()
  }

  if (cleanupOnUnmount) {
    // 检查是否在组件上下文中
    const instance = getCurrentInstance()
    if (instance) {
      onUnmounted(() => {
        cancel()
      })
    }
  }

  return {
    isActive,
    start,
    cancel,
    reset
  }
}
