import { useTaskStore } from '@stores/taskStore'
import { calculateExecutionTime, formatDuration as formatDurationUtil } from '@/utils/timeUtils'
import { useTypeWriter } from './useTypeWriter'
import { startTaskExecution, getTaskDetail } from '@/api'

export function useTaskExecution(options = {}) {
  const taskStore = useTaskStore()

  const { showDecimalTime = true } = options

  const systemDisplayText = ref('')
  const isCompleted = ref(false)
  const showLoadingIcon = ref(false)
  const executionTime = ref(0)
  const timeTrackingTimer = ref(null)
  const statusCheckTimer = ref(null)
  const simulationController = ref(null)

  const { typeWriterQueue } = useTypeWriter(systemDisplayText, simulationController)

  const formatDuration = (minutes) => {
    return formatDurationUtil(minutes, showDecimalTime)
  }

  // 开始时间跟踪
  const startTimeTracking = (startDate) => {
    executionTime.value = calculateExecutionTime(startDate, null, showDecimalTime)

    timeTrackingTimer.value = setInterval(() => {
      executionTime.value = calculateExecutionTime(startDate, null, showDecimalTime)
    }, 1000)
  }

  // 开始定期检查任务状态
  const startStatusChecking = (taskId, checkCallback) => {
    if (statusCheckTimer.value) {
      clearInterval(statusCheckTimer.value)
    }

    statusCheckTimer.value = setInterval(async () => {
      await checkCallback(false)
    }, 15000)
  }

  // 清除所有定时器
  const clearTimers = () => {
    if (timeTrackingTimer.value) {
      clearInterval(timeTrackingTimer.value)
      timeTrackingTimer.value = null
    }

    if (statusCheckTimer.value) {
      clearInterval(statusCheckTimer.value)
      statusCheckTimer.value = null
    }

    if (simulationController.value) {
      simulationController.value.cancelled = true
      simulationController.value = null
    }
  }

  // 检查任务状态
  const checkTaskStatusFromDetail = async (task, isInitialCheck = true) => {
    if (isInitialCheck) {
      systemDisplayText.value = '正在获取任务详情...'
      showLoadingIcon.value = true
    } else {
      systemDisplayText.value = '正在检查任务状态...'
      showLoadingIcon.value = true
    }

    const { d: detail } = await getTaskDetail(task.taskId)

    if (detail && detail.status === '9') {
      // 任务已完成
      showLoadingIcon.value = false
      systemDisplayText.value = `已完成关于${task.sysName}目标系统的任务执行，报告内容已经生成，请通过以下路径进行查看：`
      isCompleted.value = true

      // 停止状态检查
      if (statusCheckTimer.value) {
        clearInterval(statusCheckTimer.value)
        statusCheckTimer.value = null
      }

      executionTime.value = calculateExecutionTime(task.startDate, null, showDecimalTime)

      taskStore.updateTaskStatus(task.taskId, '9', {
        endDate: detail.endDate,
        reportFileName: detail.reportFileName
      })
    } else if (detail && detail.status === '1') {
      // 任务仍在执行中
      showLoadingIcon.value = false
      systemDisplayText.value = '任务正在执行中，请稍后...'

      if (isInitialCheck) {
        startTimeTracking(task.startDate)
        startStatusChecking(task.taskId, (isInitial) => checkTaskStatusFromDetail(task, isInitial))
      }
    } else if (detail && detail.status === '-1') {
      // 任务执行失败
      showLoadingIcon.value = false
      systemDisplayText.value = '任务执行失败，请重新创建任务'

      if (statusCheckTimer.value) {
        clearInterval(statusCheckTimer.value)
        statusCheckTimer.value = null
      }

      taskStore.updateTaskStatus(task.taskId, '-1')
    } else {
      showLoadingIcon.value = false
      systemDisplayText.value = '无法获取任务状态，请检查网络连接'

      if (!isInitialCheck && statusCheckTimer.value) {
        console.log('定期检查失败，将在下次继续尝试')
      }
    }
  }

  // 新任务执行流程
  const simulateNewTaskExecution = async (task) => {
    const controller = { cancelled: false }
    simulationController.value = controller

    await typeWriterQueue('已接收到任务，开始执行，请稍后...')

    if (controller.cancelled) return

    showLoadingIcon.value = true
    startTimeTracking(task.startDate)

    //调用后台接口，开始执行任务
    try {
      const { d: result } = await startTaskExecution(task.taskId)

      if (controller.cancelled) return
      showLoadingIcon.value = false
      const resultText = `已完成关于${result.sysName}目标系统的任务执行，报告内容已经生成，请通过以下路径进行查看：`
      await typeWriterQueue(resultText)

      if (controller.cancelled) return

      isCompleted.value = true

      taskStore.updateTaskStatus(result.taskId, result.status, {
        endDate: result.endDate,
        reportFileName: result.reportFileName
      })

      simulationController.value = null
    } catch (error) {
      console.log('执行任务失败:', error)
      showLoadingIcon.value = false
      systemDisplayText.value = '任务执行失败，请重新创建任务'
      taskStore.updateTaskStatus(task.taskId, '-1')
      return
    }
  }

  // 初始化任务执行状态
  const initializeTaskExecution = async (task) => {
    console.log('initializeTaskExecution', task)
    executionTime.value = calculateExecutionTime(task.startDate, null, showDecimalTime)

    if (task.status === '9') {
      systemDisplayText.value = `已完成关于${task.sysName}目标系统的任务执行，报告内容已经生成，请通过以下路径进行查看：`
      isCompleted.value = true
      return
    }

    if (task.status === '-1') {
      systemDisplayText.value = '任务执行失败，请重新创建任务'
      return
    }

    if (task.status === '1') {
      // 使用明确的任务选择类型来判断是否是新任务
      const isNewTask = taskStore.taskSelectionType === 'NEW_TASK'
      console.log('任务执行判断 - useTaskExecution:', {
        taskName: task.sysName,
        selectionType: taskStore.taskSelectionType,
        isNewTask
      })

      if (isNewTask) {
        // 新建任务：执行完整的创建和执行流程
        await simulateNewTaskExecution(task)
        taskStore.clearTaskSelectionType()
      } else {
        // 切换到执行中任务：直接通过详情接口检查状态
        console.log('切换到执行中任务，通过详情接口检查状态:', task.sysName)
        await checkTaskStatusFromDetail(task, true)
        taskStore.clearTaskSelectionType()
      }
    }
  }

  // 重置状态
  const resetState = () => {
    systemDisplayText.value = ''
    isCompleted.value = false
    showLoadingIcon.value = false
    executionTime.value = 0
  }

  return {
    // 状态
    systemDisplayText,
    isCompleted,
    showLoadingIcon,
    executionTime,

    // 计算属性
    formatDuration,

    // 方法
    initializeTaskExecution,
    clearTimers,
    resetState
  }
}
