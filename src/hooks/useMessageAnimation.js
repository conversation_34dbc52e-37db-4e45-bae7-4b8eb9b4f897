import { gsap } from 'gsap'
export function useMessageAnimation() {
  const userMessageRef = ref(null)
  const systemMessageRef = ref(null)

  /**
   * 用户消息弹出动画 - 从下方向上弹出
   */
  const animateUserMessage = () => {
    if (!userMessageRef.value) return Promise.resolve()

    // 初始状态：从下方隐藏
    gsap.set(userMessageRef.value, {
      y: 50,
      opacity: 0,
      scale: 0.9
    })

    // 向上弹出动画
    return gsap
      .to(userMessageRef.value, {
        y: 0,
        opacity: 1,
        scale: 1,
        duration: 0.5,
        ease: 'back.out(1.7)'
      })
      .then()
  }

  /**
   * 系统消息弹出动画 - 从下方向上弹出
   */
  const animateSystemMessage = (delay = 0.3) => {
    if (!systemMessageRef.value) return Promise.resolve()

    // 初始状态：从下方隐藏
    gsap.set(systemMessageRef.value, {
      y: 30,
      opacity: 0,
      scale: 0.95
    })

    // 延迟后向上弹出动画
    return gsap
      .to(systemMessageRef.value, {
        y: 0,
        opacity: 1,
        scale: 1,
        duration: 0.4,
        ease: 'power2.out',
        delay: delay
      })
      .then()
  }

  /**
   * 消息序列动画 - 先用户消息，再系统消息
   */
  const animateMessageSequence = async () => {
    // 先播放用户消息动画
    await animateUserMessage()

    // 再播放系统消息动画（带延迟）
    await animateSystemMessage(0.3)
  }

  /**
   * 重置动画状态
   */
  const resetAnimation = () => {
    if (userMessageRef.value) {
      gsap.set(userMessageRef.value, { clearProps: 'all' })
    }
    if (systemMessageRef.value) {
      gsap.set(systemMessageRef.value, { clearProps: 'all' })
    }
  }

  return {
    userMessageRef,
    systemMessageRef,
    animateUserMessage,
    animateSystemMessage,
    animateMessageSequence,
    resetAnimation
  }
}
