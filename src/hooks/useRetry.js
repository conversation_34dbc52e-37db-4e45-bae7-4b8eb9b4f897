import { useTimeout } from '@/hooks'

/**
 * 管理错误重试逻辑的 Hook
 *
 * @param {Object} options - 配置选项
 * @param {number} options.maxRetries - 最大重试次数（默认 3）
 * @param {number} options.retryDelay - 重试延迟时间（毫秒，默认 5000）
 * @param {boolean} options.exponentialBackoff - 是否使用指数回退策略（默认 false）
 * @returns {Object} 重试控制对象
 */
export function useRetry(options = {}) {
  const { maxRetries = 3, retryDelay = 5000, exponentialBackoff = false } = options

  const retryCount = ref(0)
  const isRetrying = ref(false)
  const lastError = ref(null)

  const { cancel: cancelRetryTimeout } = useTimeout(() => {}, retryDelay)

  /**
   * 计算当前重试延迟
   * @returns {number} 当前应用的重试延迟（毫秒）
   */
  const getCurrentDelay = () => {
    if (exponentialBackoff) {
      return retryDelay * Math.pow(2, retryCount.value)
    }
    return retryDelay
  }

  /**
   * 执行带重试的异步操作
   * @param {Function} asyncOperation - 要执行的异步操作
   * @param {Function} onSuccess - 成功回调
   * @param {Function} onError - 错误回调
   * @returns {Promise} 操作结果
   */
  const executeWithRetry = async (asyncOperation, onSuccess, onError) => {
    try {
      const result = await asyncOperation()
      // 成功时重置重试计数
      retryCount.value = 0
      lastError.value = null
      isRetrying.value = false

      if (onSuccess) {
        onSuccess(result)
      }

      return result
    } catch (error) {
      lastError.value = error

      // 判断是否可以重试
      if (retryCount.value < maxRetries) {
        retryCount.value++
        isRetrying.value = true

        console.log(`操作失败，准备第 ${retryCount.value} 次重试（最大 ${maxRetries} 次）`)

        // 开始延迟重试
        return new Promise((resolve, reject) => {
          const currentDelay = getCurrentDelay()

          const { start } = useTimeout(async () => {
            try {
              // 递归调用自身进行重试
              const result = await executeWithRetry(asyncOperation, onSuccess, onError)
              resolve(result)
            } catch (retryError) {
              reject(retryError)
            }
          }, currentDelay)

          start()
        })
      }

      // 超过最大重试次数，调用错误回调并抛出异常
      isRetrying.value = false
      if (onError) {
        onError(error, retryCount.value)
      }
      throw error
    }
  }

  /**
   * 手动触发重试
   * @param {Function} asyncOperation - 要重试的异步操作
   */
  const retry = async (asyncOperation) => {
    if (asyncOperation && lastError.value) {
      retryCount.value = 0
      return executeWithRetry(asyncOperation)
    }
    return Promise.reject(new Error('No operation to retry'))
  }

  /**
   * 取消当前重试
   */
  const cancelRetry = () => {
    cancelRetryTimeout()
    isRetrying.value = false
  }

  /**
   * 重置重试状态
   */
  const reset = () => {
    cancelRetry()
    retryCount.value = 0
    lastError.value = null
    isRetrying.value = false
  }

  return {
    retryCount,
    isRetrying,
    lastError,
    maxRetries,
    executeWithRetry,
    retry,
    cancelRetry,
    reset
  }
}
