import { gsap } from 'gsap'

/**
 * 检查元素是否为有效的DOM元素
 */
const isValidDOMElement = (element) => {
  return (
    element &&
    element.nodeType === Node.ELEMENT_NODE &&
    element.parentNode &&
    typeof element.getBoundingClientRect === 'function'
  )
}

/**
 * 安全的GSAP设置函数
 */
const safeGsapSet = (target, props) => {
  if (!isValidDOMElement(target)) {
    return
  }

  try {
    gsap.set(target, props)
  } catch (error) {
    console.warn('safeGsapSet: GSAP设置失败', error)
  }
}

/**
 * 安全的GSAP动画函数
 */
const safeGsapTo = (target, props) => {
  if (!isValidDOMElement(target)) {
    return gsap.timeline()
  }

  try {
    return gsap.to(target, props)
  } catch (error) {
    console.warn('safeGsapTo: GSAP动画失败', error)
    return gsap.timeline()
  }
}

export function usePageAnimation() {
  /**
   * 初始化页面进入动画
   * @param {Object} options 动画配置选项
   * @param {Ref} options.headerRef 头部元素引用
   * @param {Ref} options.formRef 表单元素引用
   * @param {Object} options.config 动画配置
   */
  const initPageAnimation = (options = {}) => {
    const { headerRef, formRef, config = {} } = options

    // 默认配置
    const defaultConfig = {
      // 初始位移距离
      initialY: -30,
      // 头部动画配置
      header: {
        duration: 0.8,
        ease: 'power2.out',
        delay: 0
      },
      // 表单动画配置
      form: {
        duration: 0.6,
        ease: 'power2.out',
        delay: 0.5 // 相对于头部动画的延迟
      },
      // 动画重叠时间（负值表示重叠）
      overlap: -0.3
    }

    // 合并配置
    const finalConfig = {
      ...defaultConfig,
      ...config,
      header: { ...defaultConfig.header, ...config.header },
      form: { ...defaultConfig.form, ...config.form }
    }

    // 验证必要的引用
    if (!headerRef?.value || !formRef?.value) {
      console.warn('usePageAnimation: headerRef 或 formRef 未提供或未挂载')
      return
    }

    // 初始状态：隐藏所有元素
    safeGsapSet(headerRef.value, {
      opacity: 0,
      y: finalConfig.initialY
    })
    safeGsapSet(formRef.value, {
      opacity: 0,
      y: finalConfig.initialY
    })

    // 创建时间线动画
    const tl = gsap.timeline()

    // 1. 头部元素先出现（淡入 + 从上往下）
    const headerAnimation = safeGsapTo(headerRef.value, {
      opacity: 1,
      y: 0,
      duration: finalConfig.header.duration,
      ease: finalConfig.header.ease,
      delay: finalConfig.header.delay
    })
    tl.add(headerAnimation)

    // 2. 表单延迟出现（淡入 + 从下往上滑动）
    const formAnimation = safeGsapTo(formRef.value, {
      opacity: 1,
      y: 0,
      duration: finalConfig.form.duration,
      ease: finalConfig.form.ease
    })
    tl.add(formAnimation, finalConfig.overlap) // 与上一个动画重叠

    return tl
  }

  /**
   * 适用于具有 headerRef 和 formRef 的标准页面布局
   * @param {Ref} headerRef 头部元素引用
   * @param {Ref} formRef 表单元素引用
   * @param {Object} customConfig 自定义配置
   */
  const initStandardPageAnimation = (headerRef, formRef, customConfig = {}) => {
    return nextTick(() => {
      initPageAnimation({
        headerRef,
        formRef,
        config: customConfig
      })
    })
  }

  /**
   * 创建自定义序列动画
   * @param {Array} elements 元素配置数组
   * @param {Object} globalConfig 全局配置
   */
  const createSequenceAnimation = (elements = [], globalConfig = {}) => {
    const defaultGlobalConfig = {
      initialY: -30,
      stagger: 0.3, // 元素间的间隔时间
      ease: 'power2.out'
    }

    const finalGlobalConfig = { ...defaultGlobalConfig, ...globalConfig }

    if (!elements.length) {
      console.warn('usePageAnimation: elements 数组为空')
      return
    }

    const validElements = elements.filter((el) => el.ref?.value)

    if (!validElements.length) {
      console.warn('usePageAnimation: 没有有效的元素引用')
      return
    }

    // 初始状态：隐藏所有元素
    validElements.forEach((element) => {
      gsap.set(element.ref.value, {
        opacity: 0,
        y: element.initialY || finalGlobalConfig.initialY
      })
    })

    // 创建时间线动画
    const tl = gsap.timeline()

    // 按顺序添加动画
    validElements.forEach((element, index) => {
      const elementConfig = {
        duration: 0.6,
        ease: finalGlobalConfig.ease,
        delay: 0,
        ...element.config
      }

      const startTime = index === 0 ? 0 : `+=${finalGlobalConfig.stagger}`

      tl.to(
        element.ref.value,
        {
          opacity: 1,
          y: 0,
          duration: elementConfig.duration,
          ease: elementConfig.ease,
          delay: elementConfig.delay
        },
        startTime
      )
    })

    return tl
  }

  /**
   * 重置动画状态
   * @param {Array} refs 元素引用数组
   */
  const resetAnimation = (refs = []) => {
    refs.forEach((ref) => {
      if (ref?.value) {
        gsap.set(ref.value, {
          opacity: 0,
          y: -30
        })
      }
    })
  }

  return {
    initPageAnimation,
    initStandardPageAnimation,
    createSequenceAnimation,
    resetAnimation
  }
}
