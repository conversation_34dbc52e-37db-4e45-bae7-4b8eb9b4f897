/**
 * 自定义 useVModel Hook
 * 用于实现组件的双向数据绑定
 *
 * 原理：
 * 1. 使用 computed 创建计算属性
 * 2. getter 返回 props 中的值
 * 3. setter 通过 emit 触发更新事件
 *
 * @param {Object} props - 组件的 props 对象
 * @param {string} key - 要绑定的 prop 键名，默认为 'modelValue'
 * @param {Function} emit - 组件的 emit 函数
 * @param {Object} options - 配置选项
 * @param {boolean} options.passive - 是否为被动模式，默认为 false
 * @param {string} options.eventName - 自定义事件名，默认为 'update:${key}'
 * @returns {WritableComputedRef} 可写的计算属性引用
 */
export function useVModel(props, key = 'modelValue', emit, options = {}) {
  const { passive = false, eventName } = options

  // 生成事件名称
  const event = eventName || (key === 'modelValue' ? 'update:modelValue' : `update:${key}`)

  // 创建计算属性
  const proxy = computed({
    get() {
      // 返回 props 中对应的值
      return props[key]
    },
    set(value) {
      // 被动模式下不触发更新
      if (passive) return

      // 触发更新事件
      emit(event, value)
    }
  })

  return proxy
}

/**
 * 批量创建多个 v-model 绑定
 *
 * @param {Object} props - 组件的 props 对象
 * @param {Array<string>} keys - 要绑定的 prop 键名数组
 * @param {Function} emit - 组件的 emit 函数
 * @param {Object} options - 配置选项
 * @returns {Object} 包含所有绑定的对象
 */
export function useVModels(props, keys, emit, options = {}) {
  const result = {}

  keys.forEach((key) => {
    result[key] = useVModel(props, key, emit, options)
  })

  return result
}
