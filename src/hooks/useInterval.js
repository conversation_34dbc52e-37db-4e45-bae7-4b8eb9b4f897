/**
 * 管理定时器的 Hook，提供设置、清理、暂停和恢复功能
 *
 * @param {Function} callback - 要执行的回调函数
 * @param {number} delay - 执行间隔（毫秒）
 * @param {Object} options - 配置选项
 * @param {boolean} options.immediate - 是否立即启动定时器（默认 false）
 * @param {boolean} options.cleanupOnUnmount - 是否在组件卸载时自动清理（默认 true）
 * @returns {Object} 定时器控制对象
 */
export function useInterval(callback, delay = 1000, options = {}) {
  const { immediate = false, cleanupOnUnmount = true } = options

  const timer = ref(null)
  const isActive = ref(false)
  const remainingDelay = ref(delay)
  const lastExecutionTime = ref(null)

  /**
   * 启动定时器
   */
  const start = () => {
    if (timer.value) return

    isActive.value = true
    lastExecutionTime.value = Date.now()

    timer.value = setInterval(() => {
      lastExecutionTime.value = Date.now()
      callback()
    }, remainingDelay.value)
  }

  /**
   * 停止定时器
   */
  const stop = () => {
    if (timer.value) {
      clearInterval(timer.value)
      timer.value = null
      isActive.value = false
    }
  }

  /**
   * 暂停定时器，记住剩余时间
   */
  const pause = () => {
    if (timer.value && isActive.value) {
      clearInterval(timer.value)
      timer.value = null
      isActive.value = false

      // 计算剩余时间
      const elapsed = Date.now() - lastExecutionTime.value
      remainingDelay.value = Math.max(0, delay - elapsed)
    }
  }

  /**
   * 恢复定时器，使用剩余时间
   */
  const resume = () => {
    if (!isActive.value) {
      start()
    }
  }

  /**
   * 重置定时器，使用原始延迟
   */
  const reset = () => {
    stop()
    remainingDelay.value = delay
    start()
  }

  /**
   * 更新定时器延迟
   * @param {number} newDelay - 新的延迟时间（毫秒）
   */
  const updateDelay = (newDelay) => {
    if (newDelay !== delay) {
      stop()
      remainingDelay.value = newDelay
      if (isActive.value) {
        start()
      }
    }
  }

  // 如果设置为立即启动，则立即启动定时器
  if (immediate) {
    start()
  }

  // 组件卸载时自动清理
  if (cleanupOnUnmount) {
    // 检查是否在组件上下文中
    const instance = getCurrentInstance()
    if (instance) {
      onUnmounted(() => {
        stop()
      })
    }
  }

  return {
    isActive,
    start,
    stop,
    pause,
    resume,
    reset,
    updateDelay
  }
}
