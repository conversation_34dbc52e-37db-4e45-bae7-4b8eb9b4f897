import { gsap } from 'gsap'
import { nextTick } from 'vue'

/**
 * 检查元素是否为有效的DOM元素
 * 兼容 Vue 3 的 ref 和 Proxy 对象
 */
const isValidDOMElement = (element) => {
  if (!element) return false

  // 如果是 Vue ref，获取其 value
  if (element.value) {
    element = element.value
  }

  // 检查是否为真正的 DOM 元素
  return (
    element &&
    typeof element === 'object' &&
    element.nodeType === Node.ELEMENT_NODE &&
    element.parentNode &&
    typeof element.getBoundingClientRect === 'function'
  )
}

/**
 * 安全获取 DOM 元素
 * 处理 Vue 3 ref 和各种边界情况
 */
const getDOMElement = (elementOrRef) => {
  if (!elementOrRef) return null

  // 如果是 Vue ref，获取其 value
  let element = elementOrRef.value || elementOrRef

  // 如果是 Vue 组件实例，尝试获取其 $el
  if (element && typeof element === 'object' && element.$el) {
    element = element.$el
  }

  // 验证是否为有效的 DOM 元素
  if (isValidDOMElement(element)) {
    return element
  }

  return null
}

/**
 * 安全的GSAP设置函数
 */
const safeGsapSet = (target, props) => {
  const element = getDOMElement(target)
  if (!element) {
    console.warn('safeGsapSet: 无法获取有效的DOM元素', target)
    return
  }

  try {
    gsap.set(element, props)
  } catch (error) {
    console.warn('safeGsapSet: GSAP设置失败', error, element)
  }
}

const safeGsapTo = (target, props) => {
  const element = getDOMElement(target)
  if (!element) {
    console.warn('safeGsapTo: 无法获取有效的DOM元素', target)
    return gsap.timeline()
  }

  try {
    return gsap.to(element, props)
  } catch (error) {
    console.warn('safeGsapTo: GSAP动画失败', error, element)
    return gsap.timeline()
  }
}

export function useDashboardAnimation() {
  /**
   * 初始化Dashboard页面进入动画
   */
  const initDashboardAnimation = (refs = {}) => {
    const { sidebarRef, logoRef, newTaskBtnRef, taskSearchRef, taskListRef, userInfoRef } = refs

    // 验证必要的引用
    if (!sidebarRef?.value) {
      console.warn('useDashboardAnimation: 缺少sidebarRef引用')
      return
    }

    // 创建主时间线
    const masterTL = gsap.timeline()

    // 第一阶段：侧边栏进入（0-0.8秒）
    masterTL.add(createSidebarAnimation(sidebarRef), 0)

    // 第二阶段：侧边栏内容依次进入（0.3-1.5秒）
    if (logoRef?.value) {
      masterTL.add(createLogoAnimation(logoRef), 0.3)
    }
    if (newTaskBtnRef?.value) {
      masterTL.add(createButtonAnimation(newTaskBtnRef), 0.5)
    }
    if (taskSearchRef?.value) {
      masterTL.add(createSearchAnimation(taskSearchRef), 0.7)
    }
    if (taskListRef?.value) {
      masterTL.add(createListAnimation(taskListRef), 0.8)
    }

    // 第三阶段：用户信息区域进入（0.6秒）
    if (userInfoRef?.value) {
      masterTL.add(createUserInfoAnimation(userInfoRef), 0.6)
    }

    return masterTL
  }

  /**
   * 创建侧边栏动画
   */
  const createSidebarAnimation = (sidebarRef) => {
    const element = getDOMElement(sidebarRef)
    if (!element) {
      console.warn('createSidebarAnimation: 无法获取有效的DOM元素')
      return gsap.timeline()
    }

    // 初始状态
    safeGsapSet(sidebarRef, {
      opacity: 0,
      x: -50,
      scale: 0.95
    })

    // 侧边栏从左侧滑入
    return safeGsapTo(sidebarRef, {
      opacity: 1,
      x: 0,
      scale: 1,
      duration: 0.6,
      ease: 'power3.out'
    })
  }

  /**
   * 创建Logo动画 - 优雅的缩放进入
   */
  const createLogoAnimation = (logoRef) => {
    const element = getDOMElement(logoRef)
    if (!element) {
      console.warn('createLogoAnimation: 无法获取有效的DOM元素')
      return gsap.timeline()
    }

    safeGsapSet(logoRef, {
      opacity: 0,
      scale: 0.8,
      y: -20
    })

    return safeGsapTo(logoRef, {
      opacity: 1,
      scale: 1,
      y: 0,
      duration: 0.5,
      ease: 'back.out(1.2)'
    })
  }

  /**
   * 创建按钮动画 - 弹性进入
   */
  const createButtonAnimation = (buttonRef) => {
    const element = getDOMElement(buttonRef)
    if (!element) {
      console.warn('createButtonAnimation: 无法获取有效的DOM元素')
      return gsap.timeline()
    }

    safeGsapSet(buttonRef, {
      opacity: 0,
      scale: 0.9,
      y: 20
    })

    return safeGsapTo(buttonRef, {
      opacity: 1,
      scale: 1,
      y: 0,
      duration: 0.5,
      ease: 'back.out(1.1)'
    })
  }

  /**
   * 创建搜索区域动画 - 淡入上滑
   */
  const createSearchAnimation = (searchRef) => {
    const element = getDOMElement(searchRef)
    if (!element) {
      console.warn('createSearchAnimation: 无法获取有效的DOM元素')
      return gsap.timeline()
    }

    safeGsapSet(searchRef, {
      opacity: 0,
      y: 30
    })

    return safeGsapTo(searchRef, {
      opacity: 1,
      y: 0,
      duration: 0.6,
      ease: 'power2.out'
    })
  }

  /**
   * 创建任务列表动画 - 分层进入
   */
  const createListAnimation = (listRef) => {
    const element = getDOMElement(listRef)
    if (!element) {
      console.warn('createListAnimation: 无法获取有效的DOM元素')
      return gsap.timeline()
    }

    safeGsapSet(listRef, {
      opacity: 0,
      y: 40
    })

    // 列表容器进入
    return safeGsapTo(listRef, {
      opacity: 1,
      y: 0,
      duration: 0.6,
      ease: 'power2.out'
    })
  }

  /**
   * 创建用户信息区域动画 - 从右上角滑入
   */
  const createUserInfoAnimation = (userInfoRef) => {
    const element = getDOMElement(userInfoRef)
    if (!element) {
      console.warn('createUserInfoAnimation: 无法获取有效的DOM元素')
      return gsap.timeline()
    }

    safeGsapSet(userInfoRef, {
      opacity: 0,
      x: 30,
      y: -10
    })

    return safeGsapTo(userInfoRef, {
      opacity: 1,
      x: 0,
      y: 0,
      duration: 0.5,
      ease: 'power2.out'
    })
  }

  /**
   * 快速初始化标准Dashboard动画
   */
  const initStandardDashboardAnimation = (refs) => {
    return nextTick(() => {
      initDashboardAnimation(refs)
    })
  }

  /**
   * 创建页面切换动画
   */
  const createPageTransition = (fromElement, toElement) => {
    const tl = gsap.timeline()

    if (fromElement) {
      tl.to(fromElement, {
        opacity: 0,
        scale: 0.95,
        duration: 0.3,
        ease: 'power2.in'
      })
    }

    if (toElement) {
      gsap.set(toElement, {
        opacity: 0,
        scale: 1.05
      })

      tl.to(
        toElement,
        {
          opacity: 1,
          scale: 1,
          duration: 0.4,
          ease: 'power2.out'
        },
        '-=0.1'
      )
    }

    return tl
  }

  return {
    initDashboardAnimation,
    initStandardDashboardAnimation,
    createPageTransition,
    createSidebarAnimation,
    createLogoAnimation,
    createButtonAnimation,
    createSearchAnimation,
    createListAnimation,
    createUserInfoAnimation
  }
}
