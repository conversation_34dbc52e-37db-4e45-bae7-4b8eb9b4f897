import { renderAsync } from 'docx-preview'
import { downloadTaskReport } from '@/api'

export function useDocumentPreview() {
  const drawerVisible = ref(false)
  const previewLoading = ref(false)
  const previewError = ref('')
  const previewContainer = useTemplateRef('previewContainer')
  const documentLoaded = ref(false)

  const documentMapping = [
    {
      keywords: ['服务监控', '监控'],
      fileName: '天津联通服务监控渗透复测报告-20250714.docx'
    },
    {
      keywords: ['沃秘书', 'APP', '秘书'],
      fileName: '天津联通沃秘书APP透测试报告-20250712.docx'
    },
    {
      keywords: ['沃装维', '装维'],
      fileName: '天津联通沃装维渗透测试报告-20250708.docx'
    },
    {
      keywords: ['物采', '数智平台', '采购'],
      fileName: '天津联通物采数智平台渗透测试报告-20250722.docx'
    }
  ]

  // 下载按钮状态
  const downloadButtonText = computed(() => {
    if (previewLoading.value) return '加载中...'
    if (previewError.value) return '下载文档'
    if (!documentLoaded.value) return '下载文档'
    return '下载文档'
  })

  const canDownload = computed(() => {
    return documentLoaded.value && !previewLoading.value
  })

  // 根据系统名称匹配对应的文档文件名 (暂时注释，后续从后端获取真实流)
  const getMatchedDocumentFileName = (sysName) => {
    if (!sysName) {
      return documentMapping[0].fileName
    }

    const matched = documentMapping.find((doc) =>
      doc.keywords.some((keyword) => sysName.includes(keyword))
    )

    return matched ? matched.fileName : documentMapping[0].fileName
  }

  // 预览报告
  const handlePreviewReport = () => {
    drawerVisible.value = true
  }

  const handleDownloadReport = async (task) => {
    try {
      await downloadTaskReport(
        task.taskId,
        task.reportFileName || `${task.sysName}_智能扫描报告.docx`
      )
    } catch (error) {
      ElMessage.error(error)
    }
  }

  // 加载文档预览 (暂时注释，后续从后端获取真实流)
  const loadDocumentPreview = async (task) => {
    console.log('开始加载文档预览...')
    previewLoading.value = true
    previewError.value = ''
    documentLoaded.value = false

    // try {
    //   await new Promise((resolve) => setTimeout(resolve, 1000))
    //   previewError.value = '文档预览功能开发中，请直接下载查看'
    //   documentLoaded.value = true
    // } catch (error) {
    //   console.error('预览文档失败:', error)
    //   previewError.value = error.message || '文档预览失败，请稍后重试'
    //   documentLoaded.value = false
    // } finally {
    //   previewLoading.value = false
    // }

    try {
      await nextTick()
      await new Promise((resolve) => setTimeout(resolve, 3000))
      console.log('检查预览容器:', previewContainer.value)

      if (!previewContainer.value) {
        throw new Error('预览容器未找到，请稍后重试')
      }

      previewContainer.value.innerHTML = ''
      console.log('开始获取文档...')

      const matchedFileName = getMatchedDocumentFileName(task.sysName)
      console.log('匹配到的文档:', matchedFileName, '系统名称:', task.sysName)

      const response = await fetch(`${import.meta.env.BASE_URL}docs/${matchedFileName}`)
      if (!response.ok) {
        throw new Error(`文档加载失败: ${response.status} ${response.statusText}`)
      }

      console.log('文档获取成功，开始解析...')

      const arrayBuffer = await response.arrayBuffer()

      console.log('文档解析完成，开始渲染...')

      if (!previewContainer.value) {
        throw new Error('预览容器在渲染前丢失')
      }

      await renderAsync(arrayBuffer, previewContainer.value, null, {
        className: 'docx-preview',
        inWrapper: false,
        ignoreWidth: false,
        ignoreHeight: true,
        ignoreFonts: false,
        breakPages: true,
        ignoreLastRenderedPageBreak: true,
        experimental: false,
        trimXmlDeclaration: true,
        useBase64URL: false,
        useMathMLPolyfill: false,
        renderChanges: false,
        renderComments: false,
        renderEndnotes: true,
        renderFootnotes: true,
        renderHeaders: true,
        renderFooters: true
      })

      console.log('文档渲染完成')
      documentLoaded.value = true
    } catch (error) {
      console.error('预览文档失败:', error)
      previewError.value = error.message || '文档预览失败，请稍后重试'
      documentLoaded.value = false
    } finally {
      previewLoading.value = false
    }
  }

  // 重试预览
  const retryPreview = (task) => {
    loadDocumentPreview(task)
  }

  // 关闭抽屉
  const handleDrawerClose = () => {
    drawerVisible.value = false
    previewLoading.value = false
    previewError.value = ''
    documentLoaded.value = false

    if (previewContainer.value) {
      previewContainer.value.innerHTML = ''
    }
  }

  return {
    // 状态
    drawerVisible,
    previewLoading,
    previewError,
    previewContainer,
    documentLoaded,

    // 计算属性
    downloadButtonText,
    canDownload,

    // 方法
    handlePreviewReport,
    handleDownloadReport,
    loadDocumentPreview,
    retryPreview,
    handleDrawerClose
  }
}
