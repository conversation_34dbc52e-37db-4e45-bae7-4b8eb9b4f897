# 🛡️ 智能渗透测试系统

> 基于 Vue 3 + Element Plus 的现代化渗透测试管理平台

[![Vue](https://img.shields.io/badge/Vue-3.5.17-4FC08D?style=flat-square&logo=vue.js)](https://vuejs.org/)
[![Vite](https://img.shields.io/badge/Vite-7.0.5-646CFF?style=flat-square&logo=vite)](https://vitejs.dev/)
[![Element Plus](https://img.shields.io/badge/Element%20Plus-2.10.4-409EFF?style=flat-square&logo=element)](https://element-plus.org/)
[![GSAP](https://img.shields.io/badge/GSAP-3.13.0-88CE02?style=flat-square&logo=greensock)](https://gsap.com/)
[![dayjs](https://img.shields.io/badge/dayjs-1.11.13-FF5F4C?style=flat-square)](https://day.js.org/)
[![License](https://img.shields.io/badge/License-MIT-green?style=flat-square)](LICENSE)

## 📋 项目简介

智能渗透测试系统是一个现代化的 Web 应用，提供直观的界面来管理和执行渗透测试任务。系统采用组件化架构，支持实时任务监控、自动报告生成、用户认证管理等功能。

**🚀 最新版本：v1.70** - 构建系统增强与性能优化

### 🎉 v1.70 版本亮点

- 🚀 **资源预加载系统** - 智能预加载关键资源，首屏性能提升 20-40%
- 🎨 **图标自动导入** - 150+ 图标集合按需加载，零配置使用
- 📱 **px转换重构** - 支持桌面端/移动端多场景，精确转换控制
- 🎬 **动画体验优化** - 调整动画参数，提供更自然的视觉效果
- 📊 **业务功能增强** - 任务列表新增部门信息显示
- 🔧 **构建配置简化** - 代码结构优化，依赖精简

### ✨ 核心特性

- 🎯 **任务管理** - 创建、执行、监控渗透测试任务，支持部门维度管理
- 👤 **用户认证** - 完整的登录/登出系统，支持状态持久化和路由守卫
- 📊 **实时监控** - 任务执行进度实时显示，支持打字机效果和动画反馈
- 📄 **报告生成** - 自动生成 Word 格式测试报告，支持在线预览和下载
- 💾 **状态持久化** - 关键状态本地缓存，支持页面刷新恢复
- 📱 **响应式设计** - 增强的 px 转 vw/rem 自适应方案，支持桌面端和移动端
- 🚀 **资源预加载** - 智能预加载关键资源，首屏性能提升 20-40%
- 🎨 **图标系统** - 150+ 图标集合按需加载，零配置使用
- 🎬 **动画体验** - GSAP 驱动的高性能页面动画，提供流畅的用户体验
- 🔧 **模块化架构** - API 管理层 + 组合式函数，清晰的分层设计
- ⚡ **性能优化** - 代码分割、资源压缩、构建分析，持续优化体验
- 🛠️ **开发体验** - 自研 Hook 系统，图标自动导入，丰富的调试功能

## 🎯 快速特性展示

### 图标自动导入 (v1.70)

```vue
<template>
  <!-- 无需导入，直接使用 -->
  <i-ep-user />
  <!-- Element Plus 图标 -->
  <i-tabler-home />
  <!-- Tabler 图标 -->
  <i-svg-spinners-3-dots-scale />
  <!-- 加载动画 -->
</template>
```

### 资源预加载 (v1.70)

```javascript
// 自动预加载关键资源，提升首屏性能
createPreloadPlugin({
  assets: ['/src/assets/img/logo.svg', '/src/assets/img/background.svg']
})
// 生成: <link rel="preload" href="./logo-G3QUS8NS.svg" as="image">
```

### 响应式转换 (v1.70 重构)

```vue
<template>
  <!-- 自动转换 px 为 vw/rem -->
  <div style="width: 100px; height: 50px;">
    <!-- 桌面端: width: 5.20833vw; height: 2.60417vw; -->
    <!-- 移动端: width: 6.25rem; height: 3.125rem; -->
  </div>
</template>
```

## 🚀 快速开始

### 环境要求

- Node.js >= 22.12.0
- pnpm >= 8.0.0

### 安装与运行

```bash
# 克隆项目
git clone -b prod http://************:8190/tj-psdp/cuc-tj-tj-psdp-intelligent-penetration-repository.git
cd intelligent-penetration

# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 访问应用
# http://localhost:2025/intelligent-penetration/
```

### 📦 可用脚本

```bash
# 开发
pnpm dev                    # 启动开发服务器
pnpm build                  # 构建开发版本
pnpm preview               # 预览构建结果

# 生产
pnpm build:prod            # 构建生产版本
pnpm build:test            # 构建测试版本
pnpm build:analyze         # 构建并分析

# 代码质量
pnpm lint                  # ESLint检查
pnpm lint:style            # Stylelint样式检查
pnpm lint:all              # 完整代码检查(ESLint + Stylelint)
pnpm format                # Prettier格式化

# Git 提交
pnpm commit                # 交互式提交（推荐）
pnpm commitlint            # 提交信息校验

# 清理
pnpm clean                 # 清理构建目录
pnpm build:clean           # 清理并构建
```

## 🛠️ 技术栈

### 核心框架

- **Vue 3.5.17** - 渐进式 JavaScript 框架
- **Vite 7.0.5** - 下一代前端构建工具
- **Pinia 3.0.3** - Vue 状态管理库
- **Vue Router 4.5.1** - Vue 官方路由

### UI 组件

- **Element Plus 2.10.4** - Vue 3 组件库
- **@element-plus/icons-vue** - Element Plus 图标

### 工具库

- **axios 1.10.0** - HTTP 客户端
- **dayjs 1.11.13** - 轻量级时间处理库 (替代 moment.js，体积减少 97%)
- **gsap 3.13.0** - 高性能动画引擎
- **docx-preview 0.3.5** - Word 文档在线预览
- **自研 useVModel** - 零依赖双向绑定系统 (替代 @vueuse/core)

### 开发工具

- **unplugin-auto-import** - API 自动导入
- **unplugin-vue-components** - 组件自动导入
- **unplugin-icons** - 图标自动导入 (150+ 图标集合)
- **@iconify/json** - 图标数据源
- **ESLint + Prettier** - 代码规范
- **Stylelint** - 样式代码规范
- **Husky + lint-staged** - Git Hooks
- **Commitizen + Commitlint** - 提交规范

### 构建优化

- **自研 px 转换插件** - 支持 vw/rem 多单位转换，桌面端/移动端适配
- **自研资源预加载插件** - 智能预加载关键资源，提升首屏性能
- **postcss-px-to-viewport-8-plugin** - PostCSS 8+ 兼容的 px 转 vw
- **vite-plugin-compression** - Gzip 压缩
- **rollup-plugin-visualizer** - 构建分析和可视化

## � 性能表现

### 构建性能 (v1.6 → v1.70)

| 指标     | v1.5  | v1.6      | v1.70      | 改进         |
| -------- | ----- | --------- | ---------- | ------------ |
| 构建时间 | 8.33s | 5.65s     | 5.2s       | **38% ⬆️**   |
| 包体积   | 较大  | 减少120KB | 进一步优化 | **持续优化** |
| 首屏时间 | 基准  | 优化      | 减少20-40% | **显著提升** |

### 技术债务清理

- ✅ 移除 moment.js → dayjs (体积减少 97%)
- ✅ 移除 @vueuse/core → 自研 Hook (零依赖)
- ✅ 移除 @swc/core → 精简构建链
- ✅ 图标按需加载 → 减少 90% 图标体积

## �📁 项目结构

```
intelligent-penetration/
├── .husky/                 # 🪝 Git Hooks
├── build/                  # 🏗️ 构建配置目录 (v1.6+)
│   ├── config/            # 构建配置模块
│   ├── plugins/           # 插件配置模块
│   │   ├── custom/        # 自定义插件
│   │   │   ├── preload.js         # 资源预加载插件 (v1.70)
│   │   │   ├── style-px-to-vw.js  # px转换插件 (v1.70重构)
│   │   │   └── bundle-analyzer.js # 构建分析插件
│   │   ├── vue.js         # Vue 相关插件
│   │   ├── ui.js          # UI 和图标自动导入
│   │   └── optimization.js # 优化相关插件
│   └── utils/             # 构建工具
├── docs/                   # 📚 项目文档
│   ├── 技术架构文档.md      # 技术架构详解
│   ├── auto-import-icons.md # 图标自动导入说明 (v1.70)
│   ├── preload.md         # 资源预加载说明 (v1.70)
│   ├── stylePxToVwPlugin.md # px转换插件说明 (v1.70)
│   ├── Git提交规范.md      # Git 提交规范
│   └── Stylelint配置说明.md # 样式规范说明
├── src/                   # 💻 源代码
│   ├── api/              # 🌐 API 管理层
│   │   ├── index.js      # 统一导出
│   │   ├── task.js       # 任务相关 API
│   │   └── README.md     # API 使用说明
│   ├── components/       # 🧩 业务组件
│   │   ├── TaskExecution/ # 任务执行组件模块
│   │   ├── UserDropdown.vue # 用户下拉菜单 (v1.5)
│   │   ├── TaskForm.vue  # 任务表单组件
│   │   ├── TaskList.vue  # 任务列表组件
│   │   └── TaskSearch.vue # 任务搜索组件
│   ├── hooks/            # 🎣 组合式函数
│   │   ├── useTaskExecution.js # 任务执行逻辑
│   │   ├── usePageAnimation.js # 页面动画系统 (v1.5)
│   │   ├── useDashboardAnimation.js # 仪表板动画 (v1.5)
│   │   ├── useTypeWriter.js    # 打字机效果
│   │   ├── useVModel.js        # 自研双向绑定 (v1.6)
│   │   ├── useInterval.js      # 定时器管理
│   │   └── useDocumentPreview.js # 文档预览
│   ├── request/          # 📡 HTTP 请求封装
│   │   ├── index.js      # 主入口
│   │   ├── promiseState.js # 状态管理类
│   │   ├── interceptors.js # 拦截器
│   │   └── storeHandlers.js # 状态处理
│   ├── stores/           # 🗃️ 状态管理
│   │   ├── authStore.js  # 用户认证状态 (v1.5)
│   │   └── taskStore.js  # 任务状态管理
│   ├── utils/            # 🔧 工具函数
│   │   ├── performance.js # 性能监控
│   │   ├── timeUtils.js  # 时间工具 (v1.6 dayjs重构)
│   │   └── taskStatusUtils.js # 任务状态工具
│   ├── views/            # 📄 页面组件
│   │   ├── Login.vue     # 登录页面 (v1.5)
│   │   └── Dashboard.vue # 主仪表板
│   ├── assets/           # 🎨 静态资源
│   └── main.js           # 🚪 应用入口
├── .cz-config.cjs        # 📝 Commitizen 配置
├── commitlint.config.js  # ✅ Commitlint 配置
├── eslint.config.js      # 🔍 ESLint 配置
├── vite.config.js        # ⚙️ Vite 配置文件
├── package.json          # 📦 项目配置
└── README.md             # 📖 项目说明
```

## 🔧 开发规范

### Git 提交规范

本项目使用 [约定式提交](https://www.conventionalcommits.org/zh-hans/) 规范：

```bash
# 推荐使用交互式提交
pnpm commit

# 手动提交格式
git commit -m "feat: 新增用户登录功能"
git commit -m "fix: 修复登录验证bug"
git commit -m "docs: 更新API文档"
```

详细规范请查看：[Git提交规范文档](./docs/Git提交规范.md)

### 代码规范

- 遵循 ESLint 和 Prettier 配置
- 组件命名使用 PascalCase
- 文件命名使用 kebab-case
- 提交前自动运行代码检查和格式化

## 📚 文档

### 核心文档

- [📖 技术架构文档](./docs/技术架构文档.md) - 深入了解项目架构和最佳实践
- [📝 Git提交规范](./docs/Git提交规范.md) - Git 提交规范详解
- [🎨 Stylelint配置说明](./docs/Stylelint配置说明.md) - 样式代码规范
- [🌐 API 使用指南](./src/api/README.md) - API 管理层使用说明

### 插件系统文档 (v1.70 新增)

- [🎯 自动导入图标库](./docs/auto-import-icons.md) - 150+ 图标集合使用指南
- [⚡ 资源预加载插件](./docs/preload.md) - 智能预加载系统详解
- [📱 px转换插件](./docs/stylePxToVwPlugin.md) - 响应式转换系统说明

## 🤝 贡献指南

### 开发流程

1. Fork 项目到本地
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 使用规范提交: `pnpm commit`
4. 推送分支: `git push origin feature/new-feature`
5. 创建 Pull Request

### 提交规范

- 使用 `pnpm commit` 进行交互式提交
- 提交信息会自动校验格式
- 代码会自动格式化和检查

## 📄 许可证

[MIT License](LICENSE)

## 🙋‍♂️ 支持

如有问题或建议，请提交 Issue 或联系开发团队。
