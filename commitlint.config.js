/**
 * Commitlint 配置文件
 * 用于校验提交信息格式是否符合约定式提交规范
 */

export default {
  // 继承约定式提交规范
  extends: ['@commitlint/config-conventional'],

  // 自定义规则
  rules: {
    // type 类型定义，表示 git 提交的 type 必须在以下类型范围内
    'type-enum': [
      2,
      'always',
      [
        'feat', // 新功能 feature
        'fix', // 修复 bug
        'docs', // 文档注释
        'style', // 代码格式(不影响代码运行的变动)
        'refactor', // 重构(既不增加新功能，也不是修复bug)
        'perf', // 性能优化
        'test', // 增加测试
        'chore', // 构建过程或辅助工具的变动
        'revert', // 回退
        'build', // 打包
        'ci', // CI/CD相关
      ],
    ],
    // 禁用 scope 相关规则
    'scope-empty': [0],
    'scope-case': [0],
    // subject 大小写不做校验
    'subject-case': [0],
    // subject 不允许为空
    'subject-empty': [2, 'never'],
    // subject 最大长度
    'subject-max-length': [2, 'always', 50],
    // type 不允许为空
    'type-empty': [2, 'never'],
    // type 大小写不做校验
    'type-case': [0],
    // header 最大长度
    'header-max-length': [2, 'always', 72],
    // body 前必须有空行
    'body-leading-blank': [2, 'always'],
    // footer 前必须有空行
    'footer-leading-blank': [2, 'always'],
  },
}
